// Check URL Classification - Verify that only property URLs are in scraping_queue
require('dotenv').config();
const { db, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function checkUrlClassification() {
  console.log('🔍 Checking URL Classification and Queue Status\n');
  
  try {
    // 1. Overall counts
    console.log('📊 Overall Database Counts:');
    console.log('=' .repeat(50));
    
    const discoveredCount = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls`);
    const queueCount = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue`);
    
    console.log(`   🔗 Total Discovered URLs: ${discoveredCount[0]?.count || 0}`);
    console.log(`   📋 Total Scraping Queue: ${queueCount[0]?.count || 0}`);
    
    // 2. Check discovered_urls classification
    console.log('\n🔍 Discovered URLs Classification:');
    console.log('=' .repeat(50));
    
    const discoveredStats = await db.execute(sql`
      SELECT 
        website_id,
        is_property_page,
        url_type,
        COUNT(*) as count
      FROM discovered_urls 
      GROUP BY website_id, is_property_page, url_type
      ORDER BY website_id, is_property_page DESC, url_type
    `);
    
    const discoveredGrouped = {};
    discoveredStats.forEach(row => {
      if (!discoveredGrouped[row.website_id]) {
        discoveredGrouped[row.website_id] = { property: 0, listing: 0, other: 0, total: 0 };
      }
      const count = parseInt(row.count);
      discoveredGrouped[row.website_id].total += count;
      
      if (row.is_property_page) {
        discoveredGrouped[row.website_id].property += count;
      } else if (row.url_type === 'listing') {
        discoveredGrouped[row.website_id].listing += count;
      } else {
        discoveredGrouped[row.website_id].other += count;
      }
    });
    
    Object.entries(discoveredGrouped).forEach(([websiteId, stats]) => {
      console.log(`\n   🌐 ${websiteId}:`);
      console.log(`      📊 Total: ${stats.total}`);
      console.log(`      🏠 Property pages: ${stats.property}`);
      console.log(`      📋 Listing pages: ${stats.listing}`);
      console.log(`      📄 Other pages: ${stats.other}`);
      
      const propertyPercentage = stats.total > 0 ? Math.round((stats.property / stats.total) * 100) : 0;
      console.log(`      📈 Property ratio: ${propertyPercentage}%`);
    });
    
    // 3. Check scraping_queue by website
    console.log('\n📋 Scraping Queue by Website:');
    console.log('=' .repeat(50));
    
    const queueStats = await db.execute(sql`
      SELECT 
        website_id,
        status,
        COUNT(*) as count
      FROM scraping_queue 
      GROUP BY website_id, status
      ORDER BY website_id, status
    `);
    
    const queueGrouped = {};
    queueStats.forEach(row => {
      if (!queueGrouped[row.website_id]) {
        queueGrouped[row.website_id] = { total: 0, pending: 0, completed: 0, failed: 0, processing: 0 };
      }
      const count = parseInt(row.count);
      queueGrouped[row.website_id].total += count;
      queueGrouped[row.website_id][row.status] = count;
    });
    
    Object.entries(queueGrouped).forEach(([websiteId, stats]) => {
      console.log(`\n   🌐 ${websiteId}:`);
      console.log(`      📊 Total in queue: ${stats.total}`);
      console.log(`      ⏳ Pending: ${stats.pending || 0}`);
      console.log(`      ✅ Completed: ${stats.completed || 0}`);
      console.log(`      ❌ Failed: ${stats.failed || 0}`);
      console.log(`      🔄 Processing: ${stats.processing || 0}`);
    });
    
    // 4. Cross-check: Are all queue URLs marked as property pages?
    console.log('\n🔍 Cross-Check: Queue URLs vs Property Classification:');
    console.log('=' .repeat(50));
    
    const crossCheck = await db.execute(sql`
      SELECT 
        sq.website_id,
        COUNT(*) as queue_count,
        COUNT(*) FILTER (WHERE du.is_property_page = true) as property_count,
        COUNT(*) FILTER (WHERE du.is_property_page = false) as non_property_count
      FROM scraping_queue sq
      LEFT JOIN discovered_urls du ON sq.url = du.url AND sq.website_id = du.website_id
      GROUP BY sq.website_id
      ORDER BY sq.website_id
    `);
    
    let totalMismatches = 0;
    
    crossCheck.forEach(row => {
      const queueCount = parseInt(row.queue_count);
      const propertyCount = parseInt(row.property_count);
      const nonPropertyCount = parseInt(row.non_property_count);
      const mismatches = nonPropertyCount;
      
      totalMismatches += mismatches;
      
      console.log(`\n   🌐 ${row.website_id}:`);
      console.log(`      📋 URLs in queue: ${queueCount}`);
      console.log(`      ✅ Marked as property: ${propertyCount}`);
      console.log(`      ❌ NOT marked as property: ${nonPropertyCount}`);
      
      if (mismatches > 0) {
        console.log(`      ⚠️  MISMATCH: ${mismatches} non-property URLs in queue!`);
      } else {
        console.log(`      ✅ All queue URLs correctly marked as property pages`);
      }
    });
    
    // 5. Sample URLs for manual inspection
    console.log('\n🔍 Sample URLs for Manual Inspection:');
    console.log('=' .repeat(50));
    
    for (const websiteId of Object.keys(discoveredGrouped)) {
      console.log(`\n   🌐 ${websiteId} - Sample URLs:`);
      
      // Get 3 property URLs
      const propertyUrls = await db.execute(sql`
        SELECT url, confidence_score, classification_reason
        FROM discovered_urls 
        WHERE website_id = ${websiteId} AND is_property_page = true
        LIMIT 3
      `);
      
      console.log(`      🏠 Property URLs (${propertyUrls.length}):`);
      propertyUrls.forEach((url, i) => {
        console.log(`         ${i + 1}. ${url.url.substring(0, 80)}...`);
        console.log(`            📊 Confidence: ${url.confidence_score}, Reason: ${url.classification_reason}`);
      });
      
      // Get 3 non-property URLs
      const nonPropertyUrls = await db.execute(sql`
        SELECT url, url_type, confidence_score, classification_reason
        FROM discovered_urls 
        WHERE website_id = ${websiteId} AND is_property_page = false
        LIMIT 3
      `);
      
      console.log(`      📋 Non-Property URLs (${nonPropertyUrls.length}):`);
      nonPropertyUrls.forEach((url, i) => {
        console.log(`         ${i + 1}. ${url.url.substring(0, 80)}...`);
        console.log(`            📊 Type: ${url.url_type}, Confidence: ${url.confidence_score}`);
      });
    }
    
    // 6. Summary and recommendations
    console.log('\n🎯 SUMMARY AND RECOMMENDATIONS:');
    console.log('=' .repeat(50));
    
    const totalDiscovered = parseInt(discoveredCount[0]?.count || 0);
    const totalQueued = parseInt(queueCount[0]?.count || 0);
    const totalProperty = Object.values(discoveredGrouped).reduce((sum, stats) => sum + stats.property, 0);
    
    console.log(`\n📊 Overall Statistics:`);
    console.log(`   🔗 Total URLs discovered: ${totalDiscovered.toLocaleString()}`);
    console.log(`   🏠 URLs classified as property: ${totalProperty.toLocaleString()}`);
    console.log(`   📋 URLs in scraping queue: ${totalQueued.toLocaleString()}`);
    
    const propertyRatio = totalDiscovered > 0 ? Math.round((totalProperty / totalDiscovered) * 100) : 0;
    const queueRatio = totalProperty > 0 ? Math.round((totalQueued / totalProperty) * 100) : 0;
    
    console.log(`\n📈 Ratios:`);
    console.log(`   🏠 Property pages: ${propertyRatio}% of discovered URLs`);
    console.log(`   📋 Queue coverage: ${queueRatio}% of property pages`);
    
    if (totalMismatches > 0) {
      console.log(`\n⚠️  ISSUES FOUND:`);
      console.log(`   ❌ ${totalMismatches} non-property URLs found in scraping queue`);
      console.log(`   🔧 Recommendation: Clean up queue to remove non-property URLs`);
    } else {
      console.log(`\n✅ CLASSIFICATION LOOKS GOOD:`);
      console.log(`   ✅ All queue URLs are properly classified as property pages`);
      console.log(`   ✅ Queue contains ${totalQueued.toLocaleString()} property URLs ready for scraping`);
    }
    
    if (queueRatio < 90) {
      console.log(`\n⚠️  QUEUE COVERAGE:`);
      console.log(`   📊 Only ${queueRatio}% of property pages are in queue`);
      console.log(`   🔧 Some property URLs may not have been added to queue`);
    }
    
    console.log('\n🎉 URL classification check completed!');
    
  } catch (error) {
    console.error('❌ Error checking URL classification:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the check
checkUrlClassification()
  .then(() => {
    console.log('✅ URL classification check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Check failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
