// Test script to validate the fixed scraping system
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, testConnection } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function testFixedScraping() {
  console.log('🧪 Testing Fixed Scraping System');
  console.log('=' .repeat(50));
  
  try {
    // Test database connection
    console.log('🔍 Testing database connection...');
    const connected = await testConnection();
    if (!connected) {
      console.log('❌ Database connection failed');
      return;
    }
    
    // Show initial database state
    console.log('\n📊 Initial Database State:');
    const initialCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`Properties in database: ${initialCount[0].count}`);
    
    // Test URLs - mix of sale and rental properties
    const testUrls = {
      bali_home_immo: [
        // Rental property (should use rent_price)
        'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/1-bedroom-apartment-for-monthly-rental-near-berawa-beach-lb480',
        // Sale property (should use price)
        'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/canggu/3-bedroom-family-villa-for-sale-and-rent-in-canggu-berawa-rf7661'
      ]
    };
    
    // Initialize queue manager
    const queueManager = new QueueManager();
    
    // Test each website
    for (const [websiteId, urls] of Object.entries(testUrls)) {
      console.log(`\n🌐 Testing ${websiteId} with ${urls.length} URLs...`);
      
      try {
        // Use the new processSpecificUrls method
        const results = await queueManager.processSpecificUrls(websiteId, urls);
        
        console.log(`\n📋 Results for ${websiteId}:`);
        results.forEach((result, i) => {
          const url = urls[i];
          const shortUrl = url.length > 80 ? url.substring(0, 80) + '...' : url;
          
          if (result.ok) {
            console.log(`   ✅ ${i + 1}. SUCCESS: ${shortUrl}`);
            if (result.property) {
              const prop = result.property;
              console.log(`      Title: ${prop.title}`);
              console.log(`      Category: ${prop.category}`);
              console.log(`      Type: ${prop.type}`);
              if (prop.price) {
                console.log(`      Sale Price: IDR ${prop.price.toLocaleString()}`);
              }
              if (prop.rent_price) {
                console.log(`      Rent Price: IDR ${prop.rent_price.toLocaleString()}/month`);
              }
              console.log(`      Location: ${prop.city}, ${prop.state || 'Bali'}`);
            }
          } else {
            console.log(`   ❌ ${i + 1}. FAILED: ${shortUrl}`);
            console.log(`      Error: ${result.error || 'Unknown error'}`);
          }
        });
      } catch (error) {
        console.error(`❌ Error testing ${websiteId}:`, error.message);
      }
    }
    
    // Show final database state
    console.log('\n📊 Final Database State:');
    const finalCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`Properties in database: ${finalCount[0].count}`);
    console.log(`New properties added: ${finalCount[0].count - initialCount[0].count}`);
    
    // Show recent properties
    console.log('\n🏠 Recent Properties:');
    const recentProperties = await db.execute(sql`
      SELECT title, category, type, price, rent_price, city, source_id, created_at 
      FROM property 
      WHERE created_at > NOW() - INTERVAL '10 minutes'
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    if (recentProperties.length > 0) {
      recentProperties.forEach((prop, i) => {
        console.log(`   ${i + 1}. ${prop.title}`);
        console.log(`      Source: ${prop.source_id}`);
        console.log(`      Category: ${prop.category} | Type: ${prop.type}`);
        if (prop.price) {
          console.log(`      Sale Price: IDR ${parseFloat(prop.price).toLocaleString()}`);
        }
        if (prop.rent_price) {
          console.log(`      Rent Price: IDR ${parseFloat(prop.rent_price).toLocaleString()}/month`);
        }
        console.log(`      Location: ${prop.city}`);
        console.log(`      Created: ${new Date(prop.created_at).toLocaleString()}`);
        console.log('');
      });
    } else {
      console.log('   No recent properties found');
    }
    
    console.log('\n🎉 Fixed scraping test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
  
  process.exit(0);
}

testFixedScraping();
