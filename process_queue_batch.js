// Process batch of URLs from scraping_queue
require('dotenv').config();

const { db, scrapingQueue } = require('./drizzle_client');
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { isNull, eq, inArray, sql } = require('drizzle-orm');

async function processBatchFromQueue(batchSize = 20) {
  console.log(`🚀 Processing batch of ${batchSize} URLs from scraping_queue...`);
  console.log('='.repeat(60));

  try {
    // Get pending URLs from queue
    const pendingUrls = await db
      .select({
        id: scrapingQueue.id,
        url: scrapingQueue.url,
        website_id: scrapingQueue.website_id,
        priority: scrapingQueue.priority,
        attempts: scrapingQueue.attempts,
        discovered_url_id: scrapingQueue.discovered_url_id
      })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.status, 'pending'))
      .orderBy(scrapingQueue.priority, scrapingQueue.created_at)
      .limit(batchSize);

    if (pendingUrls.length === 0) {
      console.log('❌ No pending URLs found in queue!');
      return;
    }

    console.log(`📋 Found ${pendingUrls.length} pending URLs to process`);

    // Group URLs by website_id
    const urlsByWebsite = {};
    pendingUrls.forEach(item => {
      if (!urlsByWebsite[item.website_id]) {
        urlsByWebsite[item.website_id] = [];
      }
      urlsByWebsite[item.website_id].push(item);
    });

    console.log(`🌐 Processing ${Object.keys(urlsByWebsite).length} different websites`);

    let totalSuccessful = 0;
    let totalFailed = 0;
    let totalProcessed = 0;

    // Process each website separately
    for (const [websiteId, websiteUrls] of Object.entries(urlsByWebsite)) {
      console.log(`\n📡 Processing ${websiteUrls.length} URLs from ${websiteId}...`);

      // Mark URLs as processing
      const urlIds = websiteUrls.map(item => item.id);
      await db
        .update(scrapingQueue)
        .set({ 
          status: 'processing',
          started_at: new Date(),
          attempts: sql`attempts + 1`
        })
        .where(inArray(scrapingQueue.id, urlIds));

      try {
        // Extract URLs and discovered_url_ids for processing
        const urls = websiteUrls.map(item => item.url);
        const discoveredUrlIds = websiteUrls.map(item => item.discovered_url_id);

        // Process with runExtractBatch, passing source_url_ids for proper linking
        const extractOptions = {
          source_url_ids: discoveredUrlIds // Pass discovered_url_ids for linking
        };
        const results = await runExtractBatch(websiteId, urls, extractOptions);
        
        if (results && results.processedResults) {
          for (let i = 0; i < results.processedResults.length; i++) {
            const result = results.processedResults[i];
            const queueItem = websiteUrls[i];
            
            if (result && result.ok && result.data) {
              // Success - mark as completed
              await db
                .update(scrapingQueue)
                .set({ 
                  status: 'completed',
                  completed_at: new Date(),
                  error_message: null
                })
                .where(eq(scrapingQueue.id, queueItem.id));
              
              console.log(`   ✅ ${queueItem.url} -> ${result.data.title}`);
              totalSuccessful++;
            } else {
              // Failed - mark as failed or retry
              const newAttempts = queueItem.attempts + 1;
              const maxAttempts = 3;
              
              if (newAttempts >= maxAttempts) {
                await db
                  .update(scrapingQueue)
                  .set({ 
                    status: 'failed',
                    completed_at: new Date(),
                    error_message: result ? (result.error || 'Processing failed') : 'No result returned'
                  })
                  .where(eq(scrapingQueue.id, queueItem.id));
                console.log(`   ❌ ${queueItem.url} -> FAILED (max attempts reached)`);
              } else {
                await db
                  .update(scrapingQueue)
                  .set({ 
                    status: 'pending',
                    started_at: null,
                    error_message: result ? (result.error || 'Processing failed') : 'No result returned'
                  })
                  .where(eq(scrapingQueue.id, queueItem.id));
                console.log(`   ⚠️  ${queueItem.url} -> RETRY (attempt ${newAttempts}/${maxAttempts})`);
              }
              totalFailed++;
            }
            totalProcessed++;
          }
        } else {
          // All URLs in this batch failed
          for (const queueItem of websiteUrls) {
            const newAttempts = queueItem.attempts + 1;
            const maxAttempts = 3;
            
            if (newAttempts >= maxAttempts) {
              await db
                .update(scrapingQueue)
                .set({ 
                  status: 'failed',
                  completed_at: new Date(),
                  error_message: 'Batch processing failed - no results returned'
                })
                .where(eq(scrapingQueue.id, queueItem.id));
            } else {
              await db
                .update(scrapingQueue)
                .set({ 
                  status: 'pending',
                  started_at: null,
                  error_message: 'Batch processing failed - no results returned'
                })
                .where(eq(scrapingQueue.id, queueItem.id));
            }
            totalFailed++;
            totalProcessed++;
          }
        }

      } catch (error) {
        console.error(`💥 Error processing ${websiteId}:`, error.message);
        
        // Mark all URLs in this batch as failed or retry
        for (const queueItem of websiteUrls) {
          const newAttempts = queueItem.attempts + 1;
          const maxAttempts = 3;
          
          if (newAttempts >= maxAttempts) {
            await db
              .update(scrapingQueue)
              .set({ 
                status: 'failed',
                completed_at: new Date(),
                error_message: error.message
              })
              .where(eq(scrapingQueue.id, queueItem.id));
          } else {
            await db
              .update(scrapingQueue)
              .set({ 
                status: 'pending',
                started_at: null,
                error_message: error.message
              })
              .where(eq(scrapingQueue.id, queueItem.id));
          }
          totalFailed++;
          totalProcessed++;
        }
      }

      // Wait between websites to avoid overwhelming servers
      if (Object.keys(urlsByWebsite).length > 1) {
        console.log('   ⏳ Waiting 3s before next website...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    console.log('\n📊 BATCH PROCESSING SUMMARY:');
    console.log('='.repeat(50));
    console.log(`📋 Total Processed: ${totalProcessed}`);
    console.log(`✅ Successful: ${totalSuccessful}`);
    console.log(`❌ Failed: ${totalFailed}`);
    console.log(`📈 Success Rate: ${totalProcessed > 0 ? ((totalSuccessful / totalProcessed) * 100).toFixed(1) : 0}%`);

    if (totalSuccessful > 0) {
      console.log(`\n🎉 Successfully added ${totalSuccessful} new properties to database!`);
    }

  } catch (error) {
    console.error('💥 Batch processing failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run if called directly
if (require.main === module) {
  const batchSize = parseInt(process.argv[2]) || 20;
  
  processBatchFromQueue(batchSize)
    .then(() => {
      console.log('\n🎯 Batch processing completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Batch processing failed:', error.message);
      process.exit(1);
    });
}

module.exports = { processBatchFromQueue };
