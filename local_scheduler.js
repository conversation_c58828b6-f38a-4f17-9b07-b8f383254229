// Local Development Scheduler
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { SmartPaginationCrawler } = require('./smart_pagination_crawler');
const { SmartScrapingSystem } = require('./smart_scraping_system');

class LocalScheduler {
  constructor() {
    this.queueManager = new QueueManager();
    this.paginationCrawler = new SmartPaginationCrawler();
    this.scrapingSystem = new SmartScrapingSystem();
    this.isRunning = false;
  }

  // Process queue every 5 minutes
  async processQueueJob() {
    if (this.isRunning) {
      console.log('⏳ Queue processing already running, skipping...');
      return;
    }

    this.isRunning = true;
    console.log('\n🕐 [SCHEDULER] Processing queue...');

    try {
      const websites = ['betterplace', 'bali_home_immo', 'bali_villa_realty'];
      let totalProcessed = 0;

      for (const website of websites) {
        console.log(`📦 Processing ${website} queue...`);
        
        try {
          await this.queueManager.processQueue(website, 5); // 5 items per website
          totalProcessed += 5;
        } catch (error) {
          console.error(`❌ Error processing ${website}:`, error.message);
        }
        
        // Wait between websites
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      console.log(`✅ [SCHEDULER] Queue processing completed: ${totalProcessed} items processed`);

    } catch (error) {
      console.error('❌ [SCHEDULER] Queue processing failed:', error.message);
    } finally {
      this.isRunning = false;
    }
  }

  // Smart pagination crawl every 2 hours
  async smartPaginationJob() {
    console.log('\n🕐 [SCHEDULER] Smart pagination crawling...');

    try {
      const websites = ['bali_home_immo', 'betterplace'];
      
      for (const website of websites) {
        console.log(`🔍 Smart crawl for ${website}...`);
        
        try {
          const jobId = await this.paginationCrawler.smartCrawl(website);
          
          if (jobId) {
            console.log(`✅ Started crawl job ${jobId.substring(0, 8)}... for ${website}`);
          } else {
            console.log(`ℹ️  No new URLs to crawl for ${website}`);
          }
          
        } catch (error) {
          console.error(`❌ Smart crawl failed for ${website}:`, error.message);
        }
        
        // Wait between websites
        await new Promise(resolve => setTimeout(resolve, 10000));
      }

      console.log('✅ [SCHEDULER] Smart pagination crawling completed');

    } catch (error) {
      console.error('❌ [SCHEDULER] Smart pagination failed:', error.message);
    }
  }

  // Daily crawl check
  async dailyCrawlJob() {
    console.log('\n🕐 [SCHEDULER] Daily crawl check...');

    try {
      const results = await this.scrapingSystem.checkAndStartCrawls();
      console.log('✅ [SCHEDULER] Daily crawl check completed:', results);

    } catch (error) {
      console.error('❌ [SCHEDULER] Daily crawl check failed:', error.message);
    }
  }

  // Start the scheduler
  start() {
    console.log('🚀 [SCHEDULER] Starting local development scheduler...');
    console.log('📅 Schedule:');
    console.log('   • Queue processing: Every 5 minutes');
    console.log('   • Smart pagination: Every 2 hours');
    console.log('   • Daily crawl check: Every 24 hours');
    console.log('');

    // Process queue every 5 minutes
    const queueInterval = setInterval(() => {
      this.processQueueJob();
    }, 5 * 60 * 1000); // 5 minutes

    // Smart pagination every 2 hours
    const paginationInterval = setInterval(() => {
      this.smartPaginationJob();
    }, 2 * 60 * 60 * 1000); // 2 hours

    // Daily crawl check every 24 hours
    const dailyInterval = setInterval(() => {
      this.dailyCrawlJob();
    }, 24 * 60 * 60 * 1000); // 24 hours

    // Initial runs
    setTimeout(() => this.processQueueJob(), 10000); // Start after 10 seconds
    setTimeout(() => this.smartPaginationJob(), 60000); // Start after 1 minute

    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 [SCHEDULER] Shutting down...');
      clearInterval(queueInterval);
      clearInterval(paginationInterval);
      clearInterval(dailyInterval);
      process.exit(0);
    });

    console.log('✅ [SCHEDULER] Local scheduler started successfully');
    console.log('   Press Ctrl+C to stop');
  }

  // Manual trigger for testing
  async runOnce() {
    console.log('🧪 [SCHEDULER] Running all jobs once for testing...');
    
    await this.processQueueJob();
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    await this.smartPaginationJob();
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('✅ [SCHEDULER] Test run completed');
    process.exit(0);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const scheduler = new LocalScheduler();

  switch (command) {
    case 'start':
      scheduler.start();
      break;
      
    case 'test':
      await scheduler.runOnce();
      break;
      
    default:
      console.log('Local Development Scheduler');
      console.log('');
      console.log('Commands:');
      console.log('  start - Start the continuous scheduler');
      console.log('  test  - Run all jobs once for testing');
      console.log('');
      console.log('Example: node local_scheduler.js start');
  }
}

if (require.main === module) {
  main();
}

module.exports = { LocalScheduler };
