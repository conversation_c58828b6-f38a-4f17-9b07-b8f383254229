require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testBedroomFixes() {
  console.log('🧪 TESTING BEDROOM EXTRACTION FIXES');
  console.log('='.repeat(60));
  
  // Test the problematic properties
  const testUrls = [
    'https://betterplace.cc/buy/properties/BPVL00682', // "3BR" pattern
    'https://betterplace.cc/buy/properties/BPVF00687'  // "3-Bedroom" pattern
  ];
  
  const expected = [
    { id: 'BPVL00682', title: 'Elegant Modern Living Space 3BR Leasehold Villa', expectedBedrooms: 3 },
    { id: 'BPVF00687', title: 'Stunning 3-Bedroom Villa for Sale', expectedBedrooms: 3 }
  ];
  
  for (let i = 0; i < testUrls.length; i++) {
    const url = testUrls[i];
    const exp = expected[i];
    
    console.log(`\n${i + 1}. TESTING: ${exp.id}`);
    console.log(`   URL: ${url}`);
    console.log(`   Expected bedrooms: ${exp.expectedBedrooms}`);
    
    try {
      const result = await runExtractBatch('betterplace', [url], {});
      
      if (result && result.extractedData && result.extractedData.length > 0) {
        const data = result.extractedData[0];
        
        console.log(`   ✅ EXTRACTION RESULT:`);
        console.log(`      Title: ${data.title}`);
        console.log(`      Bedrooms: ${data.bedrooms} ${data.bedrooms === exp.expectedBedrooms ? '✅' : '❌'}`);
        console.log(`      Bathrooms: ${data.bathrooms} ${data.bathrooms && data.bathrooms <= 10 ? '✅' : '❌'}`);
        console.log(`      Category: ${data.category}`);
        console.log(`      Type: ${data.type}`);
        
        // Analyze the results
        if (data.bedrooms === exp.expectedBedrooms) {
          console.log(`   🎉 BEDROOM EXTRACTION: SUCCESS!`);
        } else {
          console.log(`   ❌ BEDROOM EXTRACTION: FAILED!`);
          console.log(`      Expected: ${exp.expectedBedrooms}, Got: ${data.bedrooms}`);
        }
        
        if (data.bathrooms && data.bathrooms <= 10) {
          console.log(`   🎉 BATHROOM EXTRACTION: REASONABLE!`);
        } else {
          console.log(`   ❌ BATHROOM EXTRACTION: SUSPICIOUS!`);
          console.log(`      Got: ${data.bathrooms} (should be 1-10)`);
        }
        
      } else {
        console.log(`   ❌ No data extracted`);
      }
      
    } catch (error) {
      console.error(`   ❌ Error: ${error.message}`);
    }
  }
}

testBedroomFixes();
