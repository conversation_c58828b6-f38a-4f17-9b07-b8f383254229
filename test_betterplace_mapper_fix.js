require('dotenv').config();
const fetch = require('node-fetch');
const { mapBetterPlace } = require('./scrape_worker/mappers');

async function testBetterPlaceMapperFix() {
  try {
    console.log('🧪 TESTING BETTERPLACE MAPPER WITH DESCRIPTION FIX...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    
    // Get the raw markdown from Firecrawl
    console.log('1. GETTING RAW MARKDOWN:');
    
    const firecrawlResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: false,
        waitFor: 3000
      })
    });
    
    const firecrawlData = await firecrawlResponse.json();
    
    if (firecrawlData.success) {
      const markdown = firecrawlData.data.markdown;
      console.log(`✅ Got markdown: ${markdown.length} characters`);
      
      // Test the full BetterPlace mapper
      console.log('\n2. TESTING BETTERPLACE MAPPER:');
      const mappedData = await mapBetterPlace({
        markdown: markdown,
        url: testUrl
      });
      
      if (mappedData) {
        console.log('🎉 SUCCESS! Mapper returned data:');
        console.log(`Title: ${mappedData.title}`);
        console.log(`Price: ${mappedData.price}`);
        console.log(`Location: ${mappedData.location}`);
        console.log(`Property Type: ${mappedData.property_type}`);
        console.log(`Bedrooms: ${mappedData.bedrooms}`);
        console.log(`Bathrooms: ${mappedData.bathrooms}`);
        console.log(`Description Length: ${mappedData.description?.length || 0} characters`);
        
        if (mappedData.description) {
          console.log(`\n📝 DESCRIPTION CONTENT:`);
          console.log(`"${mappedData.description}"`);
          
          // Check if it contains the expected BetterPlace content
          const hasExpectedContent = mappedData.description.toLowerCase().includes('imagine stepping into');
          console.log(`\n✅ Contains "imagine stepping into": ${hasExpectedContent ? 'YES' : 'NO'}`);
          
          if (hasExpectedContent) {
            console.log('🎯 PERFECT! The BetterPlace description fix is working!');
          } else {
            console.log('⚠️  Description found but not the expected BetterPlace content');
          }
        } else {
          console.log('\n❌ No description in mapped data');
        }
        
        // Show size information
        if (mappedData.size) {
          console.log(`\n📏 SIZE INFO:`);
          console.log(`Land Size: ${mappedData.size.land_size_sqm} sqm`);
          console.log(`Building Size: ${mappedData.size.building_size_sqm} sqm`);
        }
        
        // Show ownership info
        console.log(`\n🏠 OWNERSHIP INFO:`);
        console.log(`Ownership Type: ${mappedData.ownership_type}`);
        console.log(`Lease Duration: ${mappedData.lease_duration_years} years`);
        console.log(`Lease Text: ${mappedData.lease_duration_text}`);
        
        // Show amenities
        if (mappedData.amenities && mappedData.amenities.length > 0) {
          console.log(`\n🏖️ AMENITIES (${mappedData.amenities.length}):`);
          mappedData.amenities.slice(0, 5).forEach(amenity => {
            console.log(`- ${amenity}`);
          });
          if (mappedData.amenities.length > 5) {
            console.log(`... and ${mappedData.amenities.length - 5} more`);
          }
        }
        
        // Show images
        if (mappedData.media && mappedData.media.images && mappedData.media.images.length > 0) {
          console.log(`\n📸 IMAGES (${mappedData.media.images.length}):`);
          mappedData.media.images.slice(0, 3).forEach((image, i) => {
            console.log(`${i + 1}. ${image}`);
          });
          if (mappedData.media.images.length > 3) {
            console.log(`... and ${mappedData.media.images.length - 3} more images`);
          }
        }
        
      } else {
        console.log('❌ Mapper returned null - property might be SOLD/RENTED or parsing failed');
      }
    } else {
      console.log('❌ Failed to get markdown from Firecrawl');
      console.log('Error:', firecrawlData);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

testBetterPlaceMapperFix();
