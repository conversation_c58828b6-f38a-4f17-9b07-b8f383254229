// Debug Firecrawl job status
require('dotenv').config();

async function debugFirecrawlJob(jobId) {
  const apiKey = process.env.FIRECRAWL_API_KEY;
  
  try {
    console.log(`🔍 Checking Firecrawl job: ${jobId}`);
    
    const response = await fetch(`https://api.firecrawl.dev/v1/batch/scrape/${jobId}`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });

    console.log(`📊 Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API Error: ${errorText}`);
      return;
    }

    const result = await response.json();
    console.log(`📋 Job Status: ${result.status}`);
    console.log(`📊 Progress: ${result.completed || 0}/${result.total || '?'}`);
    
    if (result.error) {
      console.error(`❌ Job Error: ${result.error}`);
    }
    
    if (result.data && result.data.length > 0) {
      console.log(`📄 Data available: ${result.data.length} items`);
      
      // Check first item for details
      const firstItem = result.data[0];
      if (firstItem) {
        console.log(`🔍 First item:`, {
          success: firstItem.success,
          url: firstItem.url,
          hasMarkdown: !!firstItem.markdown,
          markdownLength: firstItem.markdown?.length || 0,
          error: firstItem.error
        });
      }
    }
    
    // Full result for debugging
    console.log(`\n📋 Full Result:`, JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error(`❌ Debug failed:`, error.message);
  }
}

// Get job ID from command line argument
const jobId = process.argv[2];
if (!jobId) {
  console.error('❌ Please provide job ID as argument');
  process.exit(1);
}

debugFirecrawlJob(jobId);
