// Website Configuration System
// Centralized configuration for all supported websites

const WEBSITE_CONFIGS = {
  betterplace: {
    id: 'betterplace',
    name: 'BetterPlace',
    domain: 'betterplace.cc',
    baseUrl: 'https://betterplace.cc',
    
    // Scraping configuration
    scraping: {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 30000
    },
    
    // URL patterns for classification
    urlPatterns: {
      sale: ['/buy/properties/'],
      rent: ['/rent/properties/'],
      listing: ['/buy/properties/', '/rent/properties/']
    },
    
    // Data extraction patterns
    extraction: {
      currency: 'USD',
      pricePatterns: [
        /Price\s*:\s*USD\s*([\d,]+)/i,
        /\$\s*([\d,]+)/i
      ],
      bedroomPatterns: [
        /([1-9]|1[0-5])\s*(?:bed|bedroom)s?(?!\d)/i,
        /(?:bed|bedroom)s?[:\s]+([1-9]|1[0-5])(?!\d)/i
      ],
      bathroomPatterns: [
        /([1-9]|1[0-5])\s*(?:bath|bathroom)s?(?!\d)/i,
        /(?:bath|bathroom)s?[:\s]+([1-9]|1[0-5])(?!\d)/i
      ]
    },
    
    // Validation rules
    validation: {
      requiredFields: ['title', 'price'], // Require price again
      priceRange: { min: 50000, max: 5000000 }, // USD - realistic range
      bedroomRange: { min: 1, max: 15 },
      skipOnMissingPrice: false // Reject properties without prices
    },
    
    // Queue configuration
    queue: {
      priority: 10,
      batchSize: 1,
      retryAttempts: 3
    }
  },

  bali_villa_realty: {
    id: 'bali_villa_realty',
    name: 'Bali Villa Realty',
    domain: 'balivillarealty.com',
    baseUrl: 'https://balivillarealty.com',
    
    scraping: {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 30000
    },
    
    urlPatterns: {
      sale: ['/for-sale/', '-for-sale-'],
      rent: ['/for-rent/', '-for-rent-'],
      listing: ['/property/']
    },
    
    extraction: {
      currency: 'USD',
      pricePatterns: [
        /\$\s*([\d,]+)/i,
        /USD\s*([\d,]+)/i
      ],
      landSizePatterns: [
        /(\d+)\s*sqm/i,
        /(\d+)\s*m2/i
      ]
    },
    
    validation: {
      requiredFields: ['title', 'price'], // Require price again
      priceRange: { min: 100000, max: 2000000 }, // USD - realistic range
      bedroomRange: { min: 1, max: 15 },
      skipOnMissingPrice: false // Reject properties without prices
    },
    
    queue: {
      priority: 10,
      batchSize: 1,
      retryAttempts: 3
    }
  },

  bali_home_immo: {
    id: 'bali_home_immo',
    name: 'Bali Home Immo',
    domain: 'bali-home-immo.com',
    baseUrl: 'https://bali-home-immo.com',
    
    scraping: {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 30000
    },
    
    urlPatterns: {
      sale: ['/for-sale/'],
      rent: ['/for-rent/'],
      listing: ['/realestate-property/']
    },
    
    extraction: {
      currency: 'IDR',
      skipPhrases: ['price on request', 'contact for price'],
      pricePatterns: [
        /IDR\s*([\d,]+)/i,
        /Rp\s*([\d,]+)/i
      ]
    },
    
    validation: {
      requiredFields: ['title', 'price'], // Require price again
      priceRange: { min: 10000000, max: 100000000000 }, // IDR - 10M to 100B (more flexible)
      skipOnMissingPrice: false // Reject properties without prices
    },
    
    queue: {
      priority: 5,
      batchSize: 1,
      retryAttempts: 2
    }
  },

  villabalisale: {
    id: 'villabalisale',
    name: 'Villa Bali Sale',
    domain: 'villabalisale.com',
    baseUrl: 'https://www.villabalisale.com',
    
    scraping: {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 30000
    },
    
    urlPatterns: {
      sale: ['/for-sale/'],
      rent: ['/for-rent/'],
      freehold: ['/freehold/'],
      leasehold: ['/leasehold/'],
      listing: ['/realestate-property/']
    },
    
    extraction: {
      currency: 'mixed', // USD and IDR
      pricePatterns: [
        /USD\s*([\d,]+)/i,
        /\$\s*([\d,]+)/i,
        /IDR\s*([\d,]+)/i
      ],
      leaseholdPatterns: [
        /leasehold\s*\/\s*(\d+)\s*years?/i,
        /lease.*?(\d+)\s*years?/i
      ],
      landSizePatterns: [
        /\*\*Land:\*\*\s*([\d.]+)\s*Are/i,
        /land.*?([\d.]+)\s*are/i
      ]
    },
    
    validation: {
      requiredFields: ['title', 'price'], // Require price again
      priceRange: { min: 10000000, max: 100000000000 }, // IDR - 10M to 100B (more flexible)
      bedroomRange: { min: 1, max: 15 },
      skipOnMissingPrice: false // Reject properties without prices
    },
    
    queue: {
      priority: 5,
      batchSize: 1,
      retryAttempts: 3
    }
  },

  bali_coconut_living: {
    id: 'bali_coconut_living',
    name: 'Bali Coconut Living',
    domain: 'balicoconutliving.com',
    baseUrl: 'https://balicoconutliving.com',

    // Scraping configuration
    scraping: {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 30000
    },

    // URL patterns for classification
    urlPatterns: {
      sale: ['/bali-villa-sale-freehold/', '/bali-villa-sale-leasehold/', '/bali-land-sale-'],
      rent: ['/bali-villa-yearly-rental/', '/bali-villa-monthly-rental/', '/property/villa-for-'],
      listing: ['/bali-villa-', '/property/']
    },

    // Data extraction patterns
    extraction: {
      currency: 'IDR',
      pricePatterns: [
        /IDR\s*([\d,.]+(?:\.000)*)/gi,  // Match IDR with dots as thousand separators
        /Price[:\s]*IDR\s*([\d,.]+(?:\.000)*)/gi,
        /IDR\s*([0-9]{1,3}(?:\.[0-9]{3})*(?:\.[0-9]{3})*)/gi,  // Specific for Indonesian format
        /\bIDR\s*([1-9]\d{0,2}(?:\.\d{3})*(?:\.\d{3})*)\b/gi,  // More precise Indonesian format
        /(?:Freehold|Yearly|Monthly)[^0-9]*IDR\s*([\d,.]+)/gi  // Context-aware patterns
      ],
      bedroomPatterns: [
        /([1-9]|1[0-5])\s*Bed/i,
        /([1-9]|1[0-5])\s*bedroom/i,
        /([1-9]|1[0-5])\s*BR/i,
        /Bedroom\(s\):\s*([1-9]|1[0-5])/i  // Specific to Bali Coconut Living format
      ],
      bathroomPatterns: [
        /([1-9]|1[0-5])\s*Bath/i,
        /([1-9]|1[0-5])\s*bathroom/i,
        /Bathroom\(s\):\s*([1-9]|1[0-5])/i  // Specific to Bali Coconut Living format
      ],
      landSizePatterns: [
        /Land Size:\s*(\d+)\s*m2/i,  // Specific to Bali Coconut Living format
        /(\d+)\s*m2/i,
        /(\d+)\s*sqm/i,
        /land.*?(\d+)\s*m2/i
      ],
      buildingSizePatterns: [
        /Building Size:\s*(\d+)\s*m2/i,  // Specific to Bali Coconut Living format
        /(\d+)\s*m2.*?building/i,
        /building.*?(\d+)\s*m2/i
      ],
      skipPhrases: ['TBA', 'To Be Announced', 'Contact for Price', 'Price on Request']
    },

    // Validation rules
    validation: {
      requiredFields: ['title'], // Don't require specific price field - let validation handle it
      priceRange: { min: 10000000, max: 100000000000 }, // IDR - 10M to 100B (more flexible)
      bedroomRange: { min: 1, max: 15 },
      skipOnMissingPrice: false, // This will check for either price OR rent_price
      skipSoldProperties: true // Skip SOLD properties to keep database clean
    },

    // Queue configuration
    queue: {
      priority: 8,
      batchSize: 1,
      retryAttempts: 3
    }
  }
};

// Helper functions
function getWebsiteConfig(websiteId) {
  return WEBSITE_CONFIGS[websiteId];
}

function getAllWebsiteIds() {
  return Object.keys(WEBSITE_CONFIGS);
}

function getActiveWebsites() {
  return getAllWebsiteIds().filter(id => WEBSITE_CONFIGS[id].active !== false);
}

function isValidWebsiteId(websiteId) {
  return websiteId in WEBSITE_CONFIGS;
}

function getWebsiteByDomain(domain) {
  return Object.values(WEBSITE_CONFIGS).find(config => 
    config.domain === domain || config.baseUrl.includes(domain)
  );
}

module.exports = {
  WEBSITE_CONFIGS,
  getWebsiteConfig,
  getAllWebsiteIds,
  getActiveWebsites,
  isValidWebsiteId,
  getWebsiteByDomain
};
