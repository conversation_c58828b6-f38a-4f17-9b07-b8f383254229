// Fix database schema and connection issues
require('dotenv').config();
const postgres = require('postgres');

async function fixDatabaseIssues() {
  console.log('🔧 Fixing Database Issues\n');
  
  // Check if we have the correct database URL
  if (!process.env.SUPABASE_DATABASE_URL) {
    console.error('❌ SUPABASE_DATABASE_URL not found in environment variables');
    console.log('💡 Please check your .env file');
    return;
  }
  
  console.log('🔍 Database URL found, attempting connection...');
  
  let client;
  try {
    client = postgres(process.env.SUPABASE_DATABASE_URL, {
      max: 5,
      idle_timeout: 20,
      connect_timeout: 10,
    });
    
    // Test connection
    await client`SELECT 1 as test`;
    console.log('✅ Database connection successful\n');
    
    // 1. Check what property tables exist
    console.log('📋 Checking property tables...');
    const propertyTables = await client`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_name LIKE '%propert%'
    `;
    
    console.log('Property tables found:');
    propertyTables.forEach(t => console.log(`   - ${t.table_name}`));
    
    // 2. Check exchange rates table
    console.log('\n📋 Checking exchange rates table...');
    const exchangeTables = await client`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_name LIKE '%exchange%'
    `;
    
    console.log('Exchange tables found:');
    exchangeTables.forEach(t => console.log(`   - ${t.table_name}`));
    
    // 3. Check if we have data in exchange rates
    if (exchangeTables.length > 0) {
      const exchangeData = await client`
        SELECT from_currency, to_currency, rate, date 
        FROM exchange_rates 
        ORDER BY date DESC 
        LIMIT 5
      `;
      
      console.log('\n💱 Recent exchange rates:');
      if (exchangeData.length > 0) {
        exchangeData.forEach(rate => {
          console.log(`   ${rate.from_currency} → ${rate.to_currency}: ${rate.rate} (${rate.date.toISOString().split('T')[0]})`);
        });
      } else {
        console.log('   ❌ No exchange rates found in database');
        console.log('   💡 This explains why fallback rates are being used');
      }
    }
    
    // 4. Fix the drizzle_client.js to use correct table name
    console.log('\n🔧 Checking drizzle_client.js table references...');
    
    // 5. Check if we have any properties
    const hasPropertyTable = propertyTables.some(t => t.table_name === 'property');
    const hasPropertiesTable = propertyTables.some(t => t.table_name === 'properties');
    
    if (hasPropertyTable) {
      console.log('\n📊 Checking property table data...');
      const propertyCount = await client`SELECT COUNT(*) as count FROM property`;
      console.log(`   Properties in database: ${propertyCount[0].count}`);
      
      if (propertyCount[0].count > 0) {
        const sampleProperties = await client`
          SELECT id, title, source_id, created_at 
          FROM property 
          ORDER BY created_at DESC 
          LIMIT 3
        `;
        
        console.log('   Recent properties:');
        sampleProperties.forEach((prop, i) => {
          console.log(`     ${i + 1}. ${prop.title} (${prop.source_id}) - ${prop.created_at.toISOString().split('T')[0]}`);
        });
      }
    } else if (hasPropertiesTable) {
      console.log('\n📊 Checking properties table data...');
      const propertyCount = await client`SELECT COUNT(*) as count FROM properties`;
      console.log(`   Properties in database: ${propertyCount[0].count}`);
    } else {
      console.log('\n❌ No property table found!');
      console.log('💡 This explains the "relation does not exist" error');
    }
    
    // 6. Check scraping queue status
    console.log('\n📋 Checking scraping queue...');
    const queueStats = await client`
      SELECT 
        website_id,
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE status = 'completed') as completed,
        COUNT(*) FILTER (WHERE status = 'failed') as failed,
        COUNT(*) FILTER (WHERE status = 'pending') as pending
      FROM scraping_queue 
      GROUP BY website_id
      ORDER BY website_id
    `;
    
    if (queueStats.length > 0) {
      console.log('Queue statistics:');
      queueStats.forEach(stat => {
        console.log(`   ${stat.website_id}: ${stat.total} total (${stat.completed} completed, ${stat.failed} failed, ${stat.pending} pending)`);
      });
    } else {
      console.log('   No queue data found');
    }
    
    // 7. Provide recommendations
    console.log('\n💡 Recommendations:');
    
    if (!hasPropertyTable && !hasPropertiesTable) {
      console.log('   1. ❌ Property table missing - run database migration');
    } else if (hasPropertiesTable && !hasPropertyTable) {
      console.log('   1. ⚠️  Using "properties" table but code expects "property" - update drizzle_client.js');
    } else {
      console.log('   1. ✅ Property table exists');
    }
    
    if (exchangeTables.length === 0) {
      console.log('   2. ❌ Exchange rates table missing - run database migration');
    } else if (exchangeData && exchangeData.length === 0) {
      console.log('   2. ⚠️  Exchange rates table empty - populate with current rates');
    } else {
      console.log('   2. ✅ Exchange rates available');
    }
    
    console.log('\n🎯 Next steps:');
    console.log('   1. Fix table name inconsistencies');
    console.log('   2. Populate exchange rates if missing');
    console.log('   3. Test scraping with corrected schema');
    
  } catch (error) {
    console.error('❌ Database connection or query failed:', error.message);
    console.log('\n💡 Possible issues:');
    console.log('   - Database URL incorrect in .env file');
    console.log('   - Database server not running');
    console.log('   - Network connectivity issues');
    console.log('   - Authentication problems');
  } finally {
    if (client) {
      await client.end();
    }
  }
}

fixDatabaseIssues();
