require('dotenv').config();

async function testCssExtraction() {
  console.log('🧪 TESTING CSS-BASED BEDROOM/BATHROOM EXTRACTION...');

  // Test HTML content with the CSS classes
  const testHtml = `
    <div class="details_item__icon__Kx4DF" style="mask-image: url(&quot;/_next/static/media/bedrooms.7a6788f7.svg&quot;);"></div>
    <div class="details_item__info__zDFk7">
      <span class="details_item__info__value__ramxJ">4</span>
    </div>
    <div class="details_item__icon__Kx4DF" style="mask-image: url(&quot;/_next/static/media/bathrooms.45d31171.svg&quot;);"></div>
    <div class="details_item__info__zDFk7">
      <span class="details_item__info__value__ramxJ">4</span>
    </div>
  `;

  // Test the patterns
  const bedroomPattern = /bedrooms\.7a6788f7\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
  const bathroomPattern = /bathrooms\.45d31171\.svg.*?details_item__info__value__ramxJ">(\d+)/s;

  const bedroomMatch = testHtml.match(bedroomPattern);
  const bathroomMatch = testHtml.match(bathroomPattern);

  console.log('\n🔍 EXTRACTION RESULTS:');
  console.log(`Bedrooms: ${bedroomMatch ? bedroomMatch[1] : 'Not found'}`);
  console.log(`Bathrooms: ${bathroomMatch ? bathroomMatch[1] : 'Not found'}`);

  if (bedroomMatch) {
    console.log(`✅ Bedroom pattern worked: "${bedroomMatch[0]}"`);
  }
  if (bathroomMatch) {
    console.log(`✅ Bathroom pattern worked: "${bathroomMatch[0]}"`);
  }
}

testCssExtraction();
