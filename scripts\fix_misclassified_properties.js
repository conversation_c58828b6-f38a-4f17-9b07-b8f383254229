// Fix misclassified properties using improved classification logic
require('dotenv').config();
const postgres = require('postgres');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

// Improved classification function
function classifyProperty(title, description) {
  const text = `${title} ${description}`.toLowerCase();

  // Commercial property detection - use more precise patterns to avoid false positives
  const commercialPatterns = [
    /\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent)\b/,
    /\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/,
    /\b(commercial\s+property|commercial\s+building|commercial\s+space)\b/,
    /\b(store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/,
    /\b(warehouse\s+for\s+sale|warehouse\s+for\s+rent|storage\s+facility)\b/,
    /\b(business\s+premises|business\s+property)\b/
  ];

  // Check if any commercial pattern matches
  const isCommercial = commercialPatterns.some(pattern => pattern.test(text));
  
  if (isCommercial) {
    if (/\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'OFFICE' };
    }
    if (/\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent|store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'RETAIL' };
    }
    if (/\b(warehouse\s+for\s+sale|warehouse\s+for\s+rent|storage\s+facility)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'WAREHOUSE' };
    }
    return { category: 'COMMERCIAL', type: 'OTHER' };
  }

  // Residential property detection
  if (text.includes('villa')) return { category: 'RESIDENTIAL', type: 'VILLA' };
  if (text.includes('apartment') || text.includes('apt')) return { category: 'RESIDENTIAL', type: 'APARTMENT' };
  if (text.includes('house') || text.includes('home')) return { category: 'RESIDENTIAL', type: 'HOUSE' };
  if (text.includes('condo') || text.includes('condominium')) return { category: 'RESIDENTIAL', type: 'CONDO' };
  if (text.includes('townhouse') || text.includes('town house')) return { category: 'RESIDENTIAL', type: 'TOWNHOUSE' };

  // Land detection
  if (text.includes('land') || text.includes('plot') || text.includes('lot') || text.includes('terrain')) return { category: 'LAND', type: 'LAND' };

  // Industrial detection
  if (text.includes('factory') || text.includes('manufacturing') || text.includes('industrial')) return { category: 'INDUSTRIAL', type: 'OTHER' };

  // Default to residential villa for Bali properties
  return { category: 'RESIDENTIAL', type: 'VILLA' };
}

async function fixMisclassifiedProperties() {
  console.log('🔧 Fixing Misclassified Properties with Improved Logic\n');
  
  try {
    // Find properties that are likely misclassified
    // Focus on COMMERCIAL | RETAIL properties that contain "villa" in title
    const suspiciousProperties = await client`
      SELECT id, title, category, type, source_id, created_at
      FROM property 
      WHERE category = 'COMMERCIAL' 
        AND type = 'RETAIL'
        AND (
          LOWER(title) LIKE '%villa%' 
          OR LOWER(title) LIKE '%house%'
          OR LOWER(title) LIKE '%apartment%'
        )
      ORDER BY created_at DESC
    `;
    
    console.log(`🔍 Found ${suspiciousProperties.length} potentially misclassified properties:`);
    
    if (suspiciousProperties.length === 0) {
      console.log('✅ No misclassified properties found!');
      return;
    }
    
    let corrected = 0;
    let unchanged = 0;
    
    for (const property of suspiciousProperties) {
      console.log(`\n🏠 Analyzing: "${property.title}"`);
      console.log(`   Current: ${property.category} | ${property.type}`);
      console.log(`   Source: ${property.source_id}`);
      console.log(`   Created: ${new Date(property.created_at).toLocaleString()}`);
      
      // Get new classification using improved logic
      const { category: newCategory, type: newType } = classifyProperty(
        property.title || '', 
        '' // We don't have description in database, use empty string
      );
      
      console.log(`   New: ${newCategory} | ${newType}`);
      
      // Check if classification changed
      if (property.category !== newCategory || property.type !== newType) {
        console.log(`   🔄 CORRECTING classification...`);
        
        // Update the property
        await client`
          UPDATE property 
          SET 
            category = ${newCategory}, 
            type = ${newType},
            updated_at = NOW()
          WHERE id = ${property.id}
        `;
        
        console.log(`   ✅ Updated to: ${newCategory} | ${newType}`);
        corrected++;
      } else {
        console.log(`   ✅ Classification is correct`);
        unchanged++;
      }
    }
    
    console.log(`\n📊 Correction Summary:`);
    console.log(`   🔧 Corrected: ${corrected} properties`);
    console.log(`   ✅ Unchanged: ${unchanged} properties`);
    
    if (corrected > 0) {
      console.log('\n🎉 Misclassified properties have been corrected!');
      
      // Show updated statistics
      console.log('\n📊 Updated Property Statistics:');
      const stats = await client`
        SELECT 
          category, 
          type, 
          COUNT(*) as count 
        FROM property 
        WHERE source_id IN ('betterplace', 'bali_home_immo', 'bali_villa_realty')
        GROUP BY category, type 
        ORDER BY category, type
      `;
      
      console.table(stats.map(stat => ({
        'Category': stat.category,
        'Type': stat.type,
        'Count': stat.count
      })));
    }
    
  } catch (error) {
    console.error('❌ Error fixing misclassified properties:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

fixMisclassifiedProperties();
