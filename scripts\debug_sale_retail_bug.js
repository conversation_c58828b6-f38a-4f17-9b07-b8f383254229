// Debug the "sale" -> "retail" bug
console.log('🐛 Debugging the "sale" -> "retail" classification bug\n');

const problematicTitle = 'Brand-new 1 Bedroom Villa for Sale Leasehold in Bali Tumbak Bayuh';
const text = problematicTitle.toLowerCase();

console.log(`📝 Title: "${problematicTitle}"`);
console.log(`📝 Lowercase: "${text}"`);

// Check for the problematic substring detection
console.log('\n🔍 Substring Analysis:');
console.log(`Contains 'sale': ${text.includes('sale')}`);
console.log(`Contains 'retail': ${text.includes('retail')}`);

// Find the position of 'sale' in the text
const saleIndex = text.indexOf('sale');
if (saleIndex !== -1) {
  console.log(`\n⚠️  Found 'sale' at position ${saleIndex}`);
  console.log(`Context: "${text.substring(Math.max(0, saleIndex-10), saleIndex+15)}"`);
  
  // Check if 'sale' is being detected as 'retail'
  const retailIndex = text.indexOf('retail');
  console.log(`'retail' found at position: ${retailIndex}`);
  
  // The bug: 'sale' contains 'ale' which might be confused with 'retail'
  // But actually, let's check if 'sale' contains 'retail' as substring
  console.log(`\n🔍 Detailed Analysis:`);
  console.log(`'sale'.includes('retail'): ${'sale'.includes('retail')}`);
  console.log(`'retail'.includes('sale'): ${'retail'.includes('sale')}`);
  
  // Check each problematic word individually
  const problematicWords = ['shop', 'office', 'retail', 'commercial', 'store', 'business'];
  problematicWords.forEach(word => {
    const found = text.includes(word);
    if (found) {
      const index = text.indexOf(word);
      console.log(`❌ Found '${word}' at position ${index}: "${text.substring(Math.max(0, index-5), index+word.length+5)}"`);
    } else {
      console.log(`✅ '${word}' not found`);
    }
  });
}

// Test the exact logic from reclassify_properties.js
function classifyBaliHomeImmoProperty(title, description) {
  const text = `${title} ${description}`.toLowerCase();
  
  console.log(`\n🧪 Testing reclassify_properties.js logic:`);
  console.log(`Input text: "${text}"`);
  
  // Commercial property detection
  if (text.includes('shop') || text.includes('office') || text.includes('retail') || 
      text.includes('commercial') || text.includes('store') || text.includes('business')) {
    
    console.log('❌ COMMERCIAL detected!');
    console.log('Matching conditions:');
    if (text.includes('shop')) console.log('  - shop: YES');
    if (text.includes('office')) console.log('  - office: YES');
    if (text.includes('retail')) console.log('  - retail: YES');
    if (text.includes('commercial')) console.log('  - commercial: YES');
    if (text.includes('store')) console.log('  - store: YES');
    if (text.includes('business')) console.log('  - business: YES');
    
    if (text.includes('office')) return { category: 'COMMERCIAL', type: 'OFFICE' };
    if (text.includes('shop') || text.includes('retail') || text.includes('store')) return { category: 'COMMERCIAL', type: 'RETAIL' };
    return { category: 'COMMERCIAL', type: 'OTHER' };
  }
  
  // Residential property detection
  if (text.includes('villa')) return { category: 'RESIDENTIAL', type: 'VILLA' };
  if (text.includes('apartment') || text.includes('apt')) return { category: 'RESIDENTIAL', type: 'APARTMENT' };
  if (text.includes('house') || text.includes('home')) return { category: 'RESIDENTIAL', type: 'HOUSE' };
  if (text.includes('condo') || text.includes('condominium')) return { category: 'RESIDENTIAL', type: 'CONDO' };
  if (text.includes('townhouse')) return { category: 'RESIDENTIAL', type: 'TOWNHOUSE' };
  
  // Land detection
  if (text.includes('land') || text.includes('plot') || text.includes('lot')) return { category: 'LAND', type: 'LAND' };
  
  // Default to residential villa for Bali properties
  return { category: 'RESIDENTIAL', type: 'VILLA' };
}

const result = classifyBaliHomeImmoProperty(problematicTitle, '');
console.log(`\n🎯 Final Result: ${result.category} | ${result.type}`);

// Let's also check what happens with different variations
console.log('\n🧪 Testing variations:');
const testCases = [
  'Villa for Sale',
  'Villa for Rent', 
  'Villa for Lease',
  'Retail Shop for Sale',
  'Office for Sale'
];

testCases.forEach(testCase => {
  const result = classifyBaliHomeImmoProperty(testCase, '');
  console.log(`"${testCase}" -> ${result.category} | ${result.type}`);
});
