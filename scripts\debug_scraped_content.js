// Debug what's actually in the scraped content from Villa Bali Sale
require('dotenv').config();
const { getKeyManager } = require('../scrape_worker/key_manager');

async function debugScrapedContent() {
  console.log('🔍 Debugging Villa Bali Sale Scraped Content\n');

  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/amed/ocean-views-three-bedroom-freehold-villa-in-amed-vl3341';

  try {
    console.log(`🔄 Scraping: ${testUrl}`);

    // Use simple Firecrawl API call
    const keyManager = getKeyManager();
    const currentKey = keyManager.getCurrentKey();

    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${currentKey.key}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['json', 'markdown', 'html'],
        jsonOptions: {
          prompt: "Extract all property information including title, price, location, bedrooms, bathrooms, size, amenities, images, and any other property details. Be very detailed and include all available information."
        },
        onlyMainContent: false,
        timeout: 60000,
        waitFor: 15000,
        removeBase64Images: true,
        blockAds: true,
        proxy: 'auto'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    const scraped = {
      success: true,
      json: data.data?.json || null,
      markdown: data.data?.markdown || null,
      html: data.data?.html || null
    };
    
    console.log('\n📊 Scrape Results:');
    console.log('='.repeat(60));
    console.log(`Success: ${scraped.success}`);
    console.log(`Has JSON: ${!!scraped.json}`);
    console.log(`Has Markdown: ${!!scraped.markdown}`);
    console.log(`Has HTML: ${!!scraped.html}`);
    
    if (scraped.json) {
      console.log('\n🔍 JSON Content:');
      console.log('-'.repeat(40));
      console.log(JSON.stringify(scraped.json, null, 2));
    }
    
    if (scraped.markdown) {
      console.log('\n📝 Markdown Content (first 500 chars):');
      console.log('-'.repeat(40));
      console.log(scraped.markdown.substring(0, 500) + '...');
    }
    
    if (scraped.html) {
      console.log('\n🌐 HTML Content (first 500 chars):');
      console.log('-'.repeat(40));
      console.log(scraped.html.substring(0, 500) + '...');
    }
    
    // Test the mapper with this data
    console.log('\n🧪 Testing Mapper:');
    console.log('='.repeat(60));
    
    const { mapVillaBaliSale } = require('../scrape_worker/mappers');
    
    const rawData = {
      url: testUrl,
      json: scraped.json,
      markdown: scraped.markdown,
      html: scraped.html
    };
    
    const mapped = await mapVillaBaliSale(rawData);
    
    console.log('✅ Mapped Result:');
    console.log(JSON.stringify(mapped, null, 2));
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

debugScrapedContent();
