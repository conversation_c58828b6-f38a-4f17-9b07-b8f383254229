// Debug sitemap detection in SmartCrawler
require('dotenv').config();
const { db, websiteConfigs } = require('../drizzle_client');
const { eq } = require('drizzle-orm');

async function debugSitemapDetection() {
  console.log('🐛 Debugging sitemap detection...\n');
  
  try {
    const websiteId = 'bali_home_immo';
    
    // Get website configuration exactly like SmartCrawler does
    const configs = await db.select().from(websiteConfigs)
      .where(eq(websiteConfigs.website_id, websiteId));
    
    if (configs.length === 0) {
      console.log(`❌ Website configuration not found: ${websiteId}`);
      return;
    }

    const config = configs[0];
    
    console.log(`📋 Configuration for ${websiteId}:`);
    console.log(`   website_id: ${config.website_id}`);
    console.log(`   name: ${config.name}`);
    console.log(`   base_url: ${config.base_url}`);
    console.log(`   is_active: ${config.is_active}`);
    console.log(`   sitemap_enabled: ${config.sitemap_enabled} (type: ${typeof config.sitemap_enabled})`);
    console.log(`   sitemap_urls: ${config.sitemap_urls} (type: ${typeof config.sitemap_urls})`);
    console.log(`   sitemap_filters: ${config.sitemap_filters}`);
    console.log(`   property_url_patterns: ${config.property_url_patterns}`);
    
    // Test the condition
    console.log(`\n🔍 Testing sitemap detection condition:`);
    console.log(`   config.sitemap_enabled = ${config.sitemap_enabled}`);
    console.log(`   config.sitemap_urls = ${config.sitemap_urls}`);
    console.log(`   Boolean(config.sitemap_enabled) = ${Boolean(config.sitemap_enabled)}`);
    console.log(`   Boolean(config.sitemap_urls) = ${Boolean(config.sitemap_urls)}`);
    console.log(`   Condition result: ${config.sitemap_enabled && config.sitemap_urls}`);
    
    if (config.sitemap_enabled && config.sitemap_urls) {
      console.log(`✅ Should use sitemap-based discovery`);
      
      // Test JSON parsing
      try {
        const sitemapUrls = JSON.parse(config.sitemap_urls || '[]');
        console.log(`   Parsed sitemap URLs: ${sitemapUrls.length} URLs`);
        sitemapUrls.forEach((url, i) => {
          console.log(`     ${i + 1}. ${url}`);
        });
      } catch (error) {
        console.log(`❌ JSON parsing failed: ${error.message}`);
      }
    } else {
      console.log(`❌ Should use traditional crawling`);
      if (!config.sitemap_enabled) {
        console.log(`   Reason: sitemap_enabled is false/null`);
      }
      if (!config.sitemap_urls) {
        console.log(`   Reason: sitemap_urls is empty/null`);
      }
    }
    
    // Test all sitemap-enabled websites
    console.log(`\n📋 Testing all sitemap-enabled websites:`);
    const allConfigs = await db.select().from(websiteConfigs)
      .where(eq(websiteConfigs.sitemap_enabled, true));
    
    for (const cfg of allConfigs) {
      const shouldUseSitemap = cfg.sitemap_enabled && cfg.sitemap_urls;
      console.log(`   ${cfg.website_id}: ${shouldUseSitemap ? '✅ Sitemap' : '❌ Traditional'}`);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
}

debugSitemapDetection();
