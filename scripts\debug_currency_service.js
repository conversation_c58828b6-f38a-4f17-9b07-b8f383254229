// Debug currency service to see why fallback is used instead of database rates
require('dotenv').config();
const postgres = require('postgres');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

async function debugCurrencyService() {
  console.log('🔍 Debugging Currency Service\n');
  
  try {
    // Check what exchange rates are in the database
    console.log('📊 Exchange Rates in Database:');
    const allRates = await client`
      SELECT from_currency, to_currency, rate, date
      FROM exchange_rates 
      ORDER BY from_currency, to_currency, date DESC
    `;
    
    if (allRates.length === 0) {
      console.log('❌ No exchange rates found in database!');
      return;
    }
    
    console.table(allRates.map(rate => ({
      'From': rate.from_currency,
      'To': rate.to_currency,
      'Rate': rate.rate,
      'Date': new Date(rate.date).toLocaleDateString()
    })));
    
    // Check specifically for USD to IDR
    console.log('\n🔍 USD to IDR Rates:');
    const usdToIdr = await client`
      SELECT from_currency, to_currency, rate, date
      FROM exchange_rates 
      WHERE from_currency = 'USD' AND to_currency = 'IDR'
      ORDER BY date DESC
      LIMIT 5
    `;
    
    if (usdToIdr.length > 0) {
      console.log('✅ USD to IDR rates found:');
      console.table(usdToIdr.map(rate => ({
        'Rate': rate.rate,
        'Date': new Date(rate.date).toLocaleDateString(),
        'Time': new Date(rate.date).toLocaleTimeString()
      })));
      
      const latestRate = usdToIdr[0];
      console.log(`\n💰 Latest USD→IDR rate: ${latestRate.rate}`);
      console.log(`📅 Date: ${new Date(latestRate.date).toLocaleString()}`);
      
    } else {
      console.log('❌ No USD to IDR rates found in database!');
    }
    
    // Test the currency service directly
    console.log('\n🧪 Testing Currency Service:');
    const { CurrencyService } = require('../scrape_worker/currency_service');
    const currencyService = new CurrencyService();
    
    console.log('\n🔄 Getting USD to IDR rate...');
    const rate = await currencyService.getExchangeRate('USD', 'IDR');
    console.log(`Result: ${rate}`);
    
    // Test conversion
    console.log('\n💵 Testing $165,000 conversion:');
    const usdAmount = 165000;
    const idrAmount = usdAmount * rate;
    console.log(`$${usdAmount.toLocaleString()} = IDR ${idrAmount.toLocaleString()}`);
    
    // Check if the issue is in the database connection
    console.log('\n🔍 Testing direct database query:');
    try {
      const directQuery = await client`
        SELECT from_currency, to_currency, rate, date
        FROM exchange_rates 
        WHERE from_currency = 'USD' AND to_currency = 'IDR'
        ORDER BY date DESC
        LIMIT 1
      `;
      
      if (directQuery.length > 0) {
        console.log('✅ Direct query successful:');
        console.log(`   Rate: ${directQuery[0].rate}`);
        console.log(`   Date: ${new Date(directQuery[0].date).toLocaleString()}`);
      } else {
        console.log('❌ Direct query returned no results');
      }
    } catch (error) {
      console.log('❌ Direct query failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  } finally {
    await client.end();
  }
}

debugCurrencyService();
