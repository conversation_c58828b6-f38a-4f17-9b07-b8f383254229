// Analyze current URL patterns and fix them for all three websites
require('dotenv').config();
const postgres = require('postgres');
const { SitemapParser } = require('../scrape_worker/sitemap_parser');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

async function analyzeAndFixUrlPatterns() {
  console.log('🔍 Analyzing and Fixing URL Patterns for All Three Websites\n');
  
  try {
    // First, let's analyze what URLs we actually have from sitemaps
    console.log('📊 Analyzing actual URLs from sitemaps...\n');
    
    await analyzeBaliHomeImmo();
    await analyzeBaliVillaRealty();
    await analyzeBetterPlace();
    
    // Now fix the patterns based on analysis
    console.log('\n🔧 Fixing URL patterns based on analysis...\n');
    await fixUrlPatterns();
    
    // Test the new patterns
    console.log('\n🧪 Testing new patterns...\n');
    await testNewPatterns();
    
    console.log('\n🎉 URL pattern analysis and fix completed!');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    console.error(error.stack);
  } finally {
    await client.end();
  }
}

async function analyzeBaliHomeImmo() {
  console.log('🌐 Analyzing Bali Home Immo URLs...');
  
  // Get sample URLs from database
  const sampleUrls = await client`
    SELECT url, url_type, is_property_page, is_listing_page, classification_reason
    FROM discovered_urls 
    WHERE website_id = 'bali_home_immo' 
      AND sitemap_url IS NOT NULL
    ORDER BY discovered_at DESC
    LIMIT 20
  `;
  
  console.log('📋 Sample URLs from Bali Home Immo:');
  sampleUrls.forEach((url, i) => {
    const type = url.is_property_page ? '🏠 Property' : url.is_listing_page ? '📋 Listing' : '📄 Other';
    console.log(`  ${i + 1}. ${type}: ${url.url}`);
  });
  
  // Analyze patterns
  const propertyUrls = sampleUrls.filter(u => u.is_property_page);
  const listingUrls = sampleUrls.filter(u => u.is_listing_page);
  
  console.log(`\n📊 Analysis: ${propertyUrls.length} properties, ${listingUrls.length} listings, ${sampleUrls.length - propertyUrls.length - listingUrls.length} other`);
  
  // Show what should be property URLs
  console.log('\n🔍 URLs that should be properties:');
  const shouldBeProperties = sampleUrls.filter(u => 
    u.url.includes('/realestate-property/') && 
    !u.url.endsWith('/realestate-property/') &&
    !u.url.includes('/for-rent/') &&
    !u.url.includes('/for-sale/') &&
    !u.url.includes('/page/')
  );
  
  shouldBeProperties.slice(0, 5).forEach((url, i) => {
    console.log(`  ${i + 1}. ${url.url}`);
  });
}

async function analyzeBaliVillaRealty() {
  console.log('\n🌐 Analyzing Bali Villa Realty URLs...');
  
  const sampleUrls = await client`
    SELECT url, url_type, is_property_page, is_listing_page
    FROM discovered_urls 
    WHERE website_id = 'bali_villa_realty' 
      AND sitemap_url IS NOT NULL
    ORDER BY discovered_at DESC
    LIMIT 20
  `;
  
  console.log('📋 Sample URLs from Bali Villa Realty:');
  sampleUrls.forEach((url, i) => {
    const type = url.is_property_page ? '🏠 Property' : url.is_listing_page ? '📋 Listing' : '📄 Other';
    console.log(`  ${i + 1}. ${type}: ${url.url}`);
  });
  
  const propertyUrls = sampleUrls.filter(u => u.is_property_page);
  const listingUrls = sampleUrls.filter(u => u.is_listing_page);
  
  console.log(`\n📊 Analysis: ${propertyUrls.length} properties, ${listingUrls.length} listings, ${sampleUrls.length - propertyUrls.length - listingUrls.length} other`);
}

async function analyzeBetterPlace() {
  console.log('\n🌐 Analyzing BetterPlace URLs...');
  
  const sampleUrls = await client`
    SELECT url, url_type, is_property_page, is_listing_page
    FROM discovered_urls 
    WHERE website_id = 'betterplace' 
      AND sitemap_url IS NOT NULL
    ORDER BY discovered_at DESC
    LIMIT 20
  `;
  
  console.log('📋 Sample URLs from BetterPlace:');
  sampleUrls.forEach((url, i) => {
    const type = url.is_property_page ? '🏠 Property' : url.is_listing_page ? '📋 Listing' : '📄 Other';
    console.log(`  ${i + 1}. ${type}: ${url.url}`);
  });
  
  const propertyUrls = sampleUrls.filter(u => u.is_property_page);
  const listingUrls = sampleUrls.filter(u => u.is_listing_page);
  
  console.log(`\n📊 Analysis: ${propertyUrls.length} properties, ${listingUrls.length} listings, ${sampleUrls.length - propertyUrls.length - listingUrls.length} other`);
}

async function fixUrlPatterns() {
  console.log('🔧 Applying improved URL patterns...');
  
  // Bali Home Immo - Fix the property pattern to be more specific
  const baliHomePatterns = {
    property_patterns: [
      "/realestate-property/[^/]+/$",  // Individual property pages
      "/property/[^/]+/$"              // Alternative property pattern
    ],
    listing_patterns: [
      "/realestate-property/$",         // Main listing page
      "/realestate-property/for-rent/$", // Rent listings
      "/realestate-property/for-sale/$", // Sale listings
      "/realestate-property/for-rent/page/", // Paginated rent listings
      "/realestate-property/for-sale/page/", // Paginated sale listings
      "/search",                        // Search pages
      "/category/",                     // Category pages
      "/featured-property/"             // Featured property listings
    ],
    exclude_patterns: [
      "/featured-property/",            // These are not individual properties
      "/faq",                          // FAQ page
      "/contact",                      // Contact page
      "/about"                         // About page
    ],
    keywords: ["bedroom", "villa", "apartment", "sale", "rent", "leasehold", "freehold", "bali"]
  };
  
  await client`
    UPDATE website_configs 
    SET property_url_patterns = ${JSON.stringify(baliHomePatterns)}
    WHERE website_id = 'bali_home_immo'
  `;
  console.log('✅ Updated Bali Home Immo patterns');
  
  // Bali Villa Realty - Improve patterns
  const baliVillaPatterns = {
    property_patterns: [
      "/property/[^/]+/$"               // Individual property pages
    ],
    listing_patterns: [
      "/properties/$",                  // Main properties page
      "/properties/",                   // Properties directory
      "/search",                        // Search pages
      "/category/",                     // Category pages
      "/type/",                        // Property type pages
      "/location/"                     // Location pages
    ],
    exclude_patterns: [
      "/contact",
      "/about",
      "/services"
    ],
    keywords: ["bedroom", "villa", "sale", "rent", "leasehold", "freehold", "bali"]
  };
  
  await client`
    UPDATE website_configs 
    SET property_url_patterns = ${JSON.stringify(baliVillaPatterns)}
    WHERE website_id = 'bali_villa_realty'
  `;
  console.log('✅ Updated Bali Villa Realty patterns');
  
  // BetterPlace - Keep existing good patterns
  const betterplacePatterns = {
    property_patterns: [
      "/buy/properties/BPVL\\d+$",      // Buy property pages
      "/rent/properties/BPVR\\d+$"      // Rent property pages
    ],
    listing_patterns: [
      "/buy/properties$",               // Buy listings
      "/rent/properties$",              // Rent listings
      "/buy/properties\\?",             // Buy with parameters
      "/rent/properties\\?",            // Rent with parameters
      "/buy/indonesia/",                // Location-based buy listings
      "/rent/indonesia/"                // Location-based rent listings
    ],
    exclude_patterns: [
      "/blog/",
      "/about",
      "/contact",
      "/help"
    ],
    keywords: ["bedroom", "bathroom", "villa", "apartment", "price", "sqm"]
  };
  
  await client`
    UPDATE website_configs 
    SET property_url_patterns = ${JSON.stringify(betterplacePatterns)}
    WHERE website_id = 'betterplace'
  `;
  console.log('✅ Updated BetterPlace patterns');
}

async function testNewPatterns() {
  console.log('🧪 Testing new patterns with sample URLs...');
  
  const parser = new SitemapParser();
  
  // Test URLs for each website
  const testUrls = {
    bali_home_immo: [
      'https://bali-home-immo.com/realestate-property/luxury-villa-canggu/',
      'https://bali-home-immo.com/realestate-property/for-rent/',
      'https://bali-home-immo.com/featured-property/beachfront-land-for-sale',
      'https://bali-home-immo.com/faq'
    ],
    bali_villa_realty: [
      'https://balivillarealty.com/property/villa-seminyak-123/',
      'https://balivillarealty.com/properties/',
      'https://balivillarealty.com/search',
      'https://balivillarealty.com/about'
    ],
    betterplace: [
      'https://betterplace.cc/buy/properties/BPVL12345',
      'https://betterplace.cc/buy/properties',
      'https://betterplace.cc/buy/indonesia/bali/',
      'https://betterplace.cc/blog/property-tips'
    ]
  };
  
  for (const [websiteId, urls] of Object.entries(testUrls)) {
    console.log(`\n🌐 Testing ${websiteId}:`);
    
    // Get the updated config
    const configs = await client`
      SELECT property_url_patterns 
      FROM website_configs 
      WHERE website_id = ${websiteId}
    `;
    
    const config = {
      website_id: websiteId,
      property_url_patterns: JSON.parse(configs[0].property_url_patterns)
    };
    
    urls.forEach(url => {
      const classification = parser.classifyUrl(url, config);
      const type = classification.isProperty ? '🏠 Property' : 
                  classification.isListing ? '📋 Listing' : '📄 Other';
      console.log(`  ${type}: ${url}`);
      console.log(`    Confidence: ${classification.confidence}, Reason: ${classification.reason}`);
    });
  }
}

analyzeAndFixUrlPatterns();
