require('dotenv').config();
const { Pool } = require('pg');

async function checkQueueTable() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🔍 Checking scraping_queue table structure...');
    
    const result = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'scraping_queue'
      ORDER BY ordinal_position
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ scraping_queue table not found');
    } else {
      console.log('✅ scraping_queue table columns:');
      result.rows.forEach(row => {
        console.log(`  - ${row.column_name}: ${row.data_type}`);
      });
    }
    
    // Also check what's in the queue
    const queueResult = await pool.query(`
      SELECT * FROM scraping_queue 
      WHERE url LIKE '%BPVL00967%'
      LIMIT 5
    `);
    
    console.log(`\n📊 BPVL00967 in queue: ${queueResult.rows.length} entries`);
    if (queueResult.rows.length > 0) {
      console.table(queueResult.rows);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkQueueTable();
