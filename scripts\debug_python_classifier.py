#!/usr/bin/env python3
# Debug the Python property classifier

def classify_type(text: str):
    """Classify property type from text - Python version"""
    text = text.lower()
    print(f"🔍 Analyzing text: '{text}'")
    
    if 'villa' in text:
        print("✅ VILLA detected")
        return 'VILLA'
    elif 'apartment' in text or 'apt' in text:
        print("✅ APARTMENT detected")
        return 'APARTMENT'
    elif 'townhouse' in text or 'town house' in text:
        print("✅ TOWNHOUSE detected")
        return 'TOWNHOUSE'
    elif 'condo' in text or 'condominium' in text:
        print("✅ CONDO detected")
        return 'CONDO'
    elif 'house' in text or 'home' in text:
        print("✅ HOUSE detected")
        return 'HOUSE'
    elif 'office' in text:
        print("❌ OFFICE detected")
        return 'OFFICE'
    elif 'retail' in text or 'shop' in text or 'store' in text:
        print("❌ RETAIL detected")
        print(f"   'retail' in text: {'retail' in text}")
        print(f"   'shop' in text: {'shop' in text}")
        print(f"   'store' in text: {'store' in text}")
        
        # Find the exact match
        if 'retail' in text:
            index = text.find('retail')
            print(f"   'retail' found at position {index}: '{text[max(0, index-10):index+15]}'")
        if 'shop' in text:
            index = text.find('shop')
            print(f"   'shop' found at position {index}: '{text[max(0, index-10):index+15]}'")
        if 'store' in text:
            index = text.find('store')
            print(f"   'store' found at position {index}: '{text[max(0, index-10):index+15]}'")
            
        return 'RETAIL'
    elif 'warehouse' in text or 'storage' in text:
        print("❌ WAREHOUSE detected")
        return 'WAREHOUSE'
    elif 'land' in text or 'plot' in text:
        print("✅ LAND detected")
        return 'LAND'
    else:
        print("✅ OTHER (default)")
        return 'OTHER'

def classify_category(property_type: str, text: str):
    """Classify property category based on type and text"""
    if property_type in ['OFFICE', 'RETAIL', 'WAREHOUSE']:
        return 'COMMERCIAL'
    elif property_type == 'LAND':
        return 'LAND'
    elif 'industrial' in text or 'factory' in text:
        return 'INDUSTRIAL'
    elif property_type in ['VILLA', 'APARTMENT', 'HOUSE', 'CONDO', 'TOWNHOUSE']:
        return 'RESIDENTIAL'
    else:
        return 'OTHER'

def test_python_classifier():
    print("🐍 Testing Python Property Classifier\n")
    
    test_cases = [
        "Brand-new 1 Bedroom Villa for Sale Leasehold in Bali Tumbak Bayuh",
        "villa Brand-new 1 Bedroom Villa for Sale Leasehold in Bali Tumbak Bayuh",
        "Modern 3-Bedroom Freehold Villa for Sale in Prime Location of Ungasan Bali",
        "Retail Shop for Sale",
        "Office Building for Sale",
        "Villa for Sale",
        "House for Sale"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"🧪 Test Case {i}: '{test_case}'")
        
        # Simulate the combined text that might be sent to classifier
        combined_text = f" {test_case} "  # property_type + title + description
        
        property_type = classify_type(combined_text)
        category = classify_category(property_type, combined_text.lower())
        
        print(f"   Result: {category} | {property_type}")
        print()

if __name__ == "__main__":
    test_python_classifier()
