// Debug URL Flow - Track exactly what happens to the source_url
require('dotenv').config();

const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugUrlFlow() {
  console.log('🔍 Debug URL Flow - Tracking source_url through pipeline');
  console.log('='.repeat(70));

  const testUrl = 'https://balicoconutliving.com/bali-villa-sale-freehold/Canggu/6267-V005-5795/Villa-Kimi-Berawa-A';
  
  console.log(`📍 Test URL: ${testUrl}`);
  console.log('');

  try {
    console.log('🚀 Step 1: Calling runExtractBatch...');
    const results = await runExtractBatch('bali_coconut_living', [testUrl], {});
    
    console.log('📊 Step 2: Analyzing results...');
    console.log('Raw results structure:', typeof results);
    console.log('Results keys:', Object.keys(results || {}));
    
    if (results && results.processedResults) {
      console.log('✅ Found processedResults');
      const processed = results.processedResults;
      console.log(`   Count: ${processed.length}`);
      
      processed.forEach((result, i) => {
        console.log(`\n📋 Result ${i + 1}:`);
        console.log(`   Type: ${typeof result}`);
        console.log(`   Keys: ${Object.keys(result || {}).join(', ')}`);
        
        if (result && result.data) {
          const data = result.data;
          console.log(`   📄 Data keys: ${Object.keys(data).join(', ')}`);
          console.log(`   🏷️  Title: ${data.title || 'NULL'}`);
          console.log(`   🔗 source_url: ${data.source_url || 'NULL ❌'}`);
          console.log(`   🆔 source_id: ${data.source_id || 'NULL'}`);
          console.log(`   🔢 external_id: ${data.external_id || 'NULL'}`);
          
          // Check media section too
          if (data.media) {
            console.log(`   📺 Media source_url: ${data.media.source_url || 'NULL'}`);
            console.log(`   📺 Media source_id: ${data.media.source_id || 'NULL'}`);
            console.log(`   📺 Media external_id: ${data.media.external_id || 'NULL'}`);
          }
        } else if (result) {
          // Direct result format
          console.log(`   🏷️  Title: ${result.title || 'NULL'}`);
          console.log(`   🔗 source_url: ${result.source_url || 'NULL ❌'}`);
          console.log(`   🆔 source_id: ${result.source_id || 'NULL'}`);
          console.log(`   🔢 external_id: ${result.external_id || 'NULL'}`);
          
          if (result.media) {
            console.log(`   📺 Media source_url: ${result.media.source_url || 'NULL'}`);
          }
        }
      });
    } else if (Array.isArray(results)) {
      console.log('✅ Found array results (legacy format)');
      results.forEach((result, i) => {
        console.log(`\n📋 Result ${i + 1}:`);
        console.log(`   🏷️  Title: ${result.title || 'NULL'}`);
        console.log(`   🔗 source_url: ${result.source_url || 'NULL ❌'}`);
        console.log(`   🆔 source_id: ${result.source_id || 'NULL'}`);
        console.log(`   🔢 external_id: ${result.external_id || 'NULL'}`);
      });
    } else {
      console.log('❌ Unexpected results format');
      console.log('Results:', JSON.stringify(results, null, 2));
    }

  } catch (error) {
    console.error('💥 Error during debug:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run if called directly
if (require.main === module) {
  debugUrlFlow()
    .then(() => {
      console.log('\n🎯 Debug completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Debug failed:', error.message);
      process.exit(1);
    });
}

module.exports = { debugUrlFlow };
