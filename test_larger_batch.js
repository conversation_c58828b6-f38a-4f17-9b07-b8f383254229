// Test script to validate the fixed scraping system with a larger batch
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, testConnection } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function testLargerBatch() {
  console.log('🧪 Testing Fixed Scraping System - Larger Batch');
  console.log('=' .repeat(60));
  
  try {
    // Test database connection
    console.log('🔍 Testing database connection...');
    const connected = await testConnection();
    if (!connected) {
      console.log('❌ Database connection failed');
      return;
    }
    
    // Show initial database state
    console.log('\n📊 Initial Database State:');
    const initialCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`Properties in database: ${initialCount[0].count}`);
    
    // Get some URLs from the queue to test with
    console.log('\n🔍 Getting URLs from queue...');
    const queueUrls = await db.execute(sql`
      SELECT url, status 
      FROM scraping_queue 
      WHERE website_id = 'bali_home_immo' 
      AND status = 'pending'
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    console.log(`Found ${queueUrls.length} URLs in queue`);
    
    if (queueUrls.length === 0) {
      console.log('⚠️ No URLs in queue, using hardcoded test URLs');
      
      // Fallback to hardcoded URLs
      const testUrls = [
        'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/canggu/3-bedroom-family-villa-for-sale-and-rent-in-canggu-berawa-rf7661',
        'https://bali-home-immo.com/realestate-property/for-sale/land/leasehold/uluwatu/breathtaking-ocean-cliff-front-land-for-sale-in-uluwatu-rf7659',
        'https://bali-home-immo.com/realestate-property/for-sale/land/leasehold/seseh/land-for-sale-leasehold-in-seseh-rf7658'
      ];
      
      await testBatch('bali_home_immo', testUrls);
    } else {
      // Use URLs from queue
      const urlsToTest = queueUrls.slice(0, 5).map(row => row.url);
      await testBatch('bali_home_immo', urlsToTest);
    }
    
    // Show final database state
    console.log('\n📊 Final Database State:');
    const finalCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`Properties in database: ${finalCount[0].count}`);
    console.log(`New properties added: ${finalCount[0].count - initialCount[0].count}`);
    
    // Show recent properties with details
    console.log('\n🏠 Recent Properties (last 10 minutes):');
    const recentProperties = await db.execute(sql`
      SELECT title, category, type, price, rent_price, city, source_id, created_at 
      FROM property 
      WHERE created_at > NOW() - INTERVAL '10 minutes'
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    if (recentProperties.length > 0) {
      recentProperties.forEach((prop, i) => {
        console.log(`   ${i + 1}. ${prop.title}`);
        console.log(`      Source: ${prop.source_id}`);
        console.log(`      Category: ${prop.category} | Type: ${prop.type}`);
        if (prop.price) {
          console.log(`      Sale Price: IDR ${parseFloat(prop.price).toLocaleString()}`);
        }
        if (prop.rent_price) {
          console.log(`      Rent Price: IDR ${parseFloat(prop.rent_price).toLocaleString()}/month`);
        }
        console.log(`      Location: ${prop.city}`);
        console.log(`      Created: ${new Date(prop.created_at).toLocaleString()}`);
        console.log('');
      });
    } else {
      console.log('   No recent properties found');
    }
    
    // Summary statistics
    console.log('\n📈 Summary Statistics:');
    const stats = await db.execute(sql`
      SELECT 
        source_id,
        COUNT(*) as total_properties,
        COUNT(CASE WHEN price IS NOT NULL THEN 1 END) as sale_properties,
        COUNT(CASE WHEN rent_price IS NOT NULL THEN 1 END) as rental_properties,
        AVG(CASE WHEN price IS NOT NULL THEN price END) as avg_sale_price,
        AVG(CASE WHEN rent_price IS NOT NULL THEN rent_price END) as avg_rent_price
      FROM property 
      WHERE created_at > NOW() - INTERVAL '1 hour'
      GROUP BY source_id
      ORDER BY total_properties DESC
    `);
    
    if (stats.length > 0) {
      console.table(stats.map(stat => ({
        Source: stat.source_id,
        Total: stat.total_properties,
        Sales: stat.sale_properties,
        Rentals: stat.rental_properties,
        'Avg Sale Price (IDR)': stat.avg_sale_price ? parseFloat(stat.avg_sale_price).toLocaleString() : 'N/A',
        'Avg Rent Price (IDR)': stat.avg_rent_price ? parseFloat(stat.avg_rent_price).toLocaleString() : 'N/A'
      })));
    }
    
    console.log('\n🎉 Larger batch test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
  
  process.exit(0);
}

async function testBatch(websiteId, urls) {
  console.log(`\n🌐 Testing ${websiteId} with ${urls.length} URLs...`);
  
  // Initialize queue manager
  const queueManager = new QueueManager();
  
  try {
    // Use the new processSpecificUrls method
    const results = await queueManager.processSpecificUrls(websiteId, urls);
    
    console.log(`\n📋 Results for ${websiteId}:`);
    let successCount = 0;
    let failCount = 0;
    
    results.forEach((result, i) => {
      const url = urls[i];
      const shortUrl = url.length > 80 ? url.substring(0, 80) + '...' : url;
      
      if (result.ok) {
        successCount++;
        console.log(`   ✅ ${i + 1}. SUCCESS: ${shortUrl}`);
        if (result.property) {
          const prop = result.property;
          console.log(`      Title: ${prop.title}`);
          if (prop.price) {
            console.log(`      Sale Price: IDR ${prop.price.toLocaleString()}`);
          }
          if (prop.rent_price) {
            console.log(`      Rent Price: IDR ${prop.rent_price.toLocaleString()}/month`);
          }
        }
      } else {
        failCount++;
        console.log(`   ❌ ${i + 1}. FAILED: ${shortUrl}`);
        console.log(`      Error: ${result.error || 'Unknown error'}`);
      }
    });
    
    console.log(`\n📊 Batch Results: ${successCount} success, ${failCount} failed`);
    console.log(`Success Rate: ${((successCount / urls.length) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error(`❌ Error testing ${websiteId}:`, error.message);
  }
}

testLargerBatch();
