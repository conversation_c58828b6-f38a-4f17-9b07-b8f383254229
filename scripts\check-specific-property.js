// Check specific property for data quality issues
require('dotenv').config();
const { db, properties } = require('../drizzle_client');
const { eq } = require('drizzle-orm');

async function checkSpecificProperty() {
  console.log('🔍 Checking specific property: 887825c1-6cc9-4cce-9408-726a1b74e590');
  
  try {
    const prop = await db
      .select()
      .from(properties)
      .where(eq(properties.id, '887825c1-6cc9-4cce-9408-726a1b74e590'))
      .limit(1);
    
    if (prop.length > 0) {
      const property = prop[0];
      console.log('\n📋 Property Details:');
      console.log('Title:', property.title);
      console.log('Price:', property.price);
      console.log('Source ID:', property.source_id);
      console.log('External ID:', property.external_id);
      console.log('Source URL ID:', property.source_url_id);
      console.log('Source URL:', property.source_url);
      
      console.log('\nDescription (first 200 chars):');
      console.log(property.description?.substring(0, 200) + '...');
      
      console.log('\nAmenities:');
      try {
        const amenities = typeof property.amenities === 'string' ? 
          JSON.parse(property.amenities) : property.amenities;
        console.log(JSON.stringify(amenities, null, 2));
      } catch (e) {
        console.log('Raw amenities:', property.amenities);
      }
      
      // Check for "Not Available" indicators
      const hasNotAvailable = 
        (property.description && property.description.toLowerCase().includes('not available')) ||
        (property.title && property.title.toLowerCase().includes('not available'));
      
      if (hasNotAvailable) {
        console.log('\n❌ ISSUE: This property contains "Not Available" text');
        console.log('   This property should have been filtered out to save API costs');
      } else {
        console.log('\n✅ No "Not Available" indicators found');
      }
      
      // Check source_url_id
      if (!property.source_url_id) {
        console.log('\n❌ ISSUE: Missing source_url_id');
      } else {
        console.log('\n✅ Has source_url_id:', property.source_url_id);
      }
      
      // Check amenities
      const amenitiesData = typeof property.amenities === 'string' ? 
        JSON.parse(property.amenities) : property.amenities;
      
      const hasAmenities = amenitiesData && 
        ((amenitiesData.raw_amenities && amenitiesData.raw_amenities.length > 0) ||
         (Array.isArray(amenitiesData) && amenitiesData.length > 0));
      
      if (!hasAmenities) {
        console.log('\n❌ ISSUE: Empty or missing amenities');
      } else {
        console.log('\n✅ Has amenities data');
      }
      
    } else {
      console.log('\n❌ Property not found in database');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkSpecificProperty().then(() => process.exit(0));
