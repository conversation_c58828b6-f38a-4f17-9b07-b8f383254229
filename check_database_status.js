const { db, testConnection } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function checkDatabase() {
  try {
    console.log('🔍 Testing database connection...');
    const connected = await testConnection();
    
    if (!connected) {
      console.log('❌ Database connection failed');
      return;
    }
    
    console.log('📊 Checking property table...');
    const propertyCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`Properties in database: ${propertyCount[0].count}`);
    
    console.log('📋 Checking sources...');
    const sources = await db.execute(sql`SELECT source_id, COUNT(*) as count FROM property GROUP BY source_id ORDER BY count DESC`);
    console.table(sources);
    
    console.log('🏗️ Checking year_built data...');
    const yearBuilt = await db.execute(sql`SELECT title, year_built FROM property WHERE year_built IS NOT NULL ORDER BY year_built DESC LIMIT 10`);
    console.table(yearBuilt);
    
    console.log('💰 Checking price data...');
    const prices = await db.execute(sql`SELECT title, price, rent_price FROM property WHERE price IS NOT NULL OR rent_price IS NOT NULL LIMIT 5`);
    console.table(prices);
    
    console.log('🔍 Checking for duplicates...');
    const duplicates = await db.execute(sql`SELECT source_id, external_id, COUNT(*) as count FROM property GROUP BY source_id, external_id HAVING COUNT(*) > 1`);
    console.log(`Duplicates found: ${duplicates.length}`);
    if (duplicates.length > 0) {
      console.table(duplicates);
    }
    
    console.log('📈 Recent scraping activity...');
    const recent = await db.execute(sql`SELECT source_id, COUNT(*) as count, MAX(created_at) as last_scraped FROM property WHERE created_at > NOW() - INTERVAL '7 days' GROUP BY source_id`);
    console.table(recent);
    
    console.log('✅ Database check complete');
    
  } catch (error) {
    console.error('❌ Database check failed:', error);
  }
  
  process.exit(0);
}

checkDatabase();
