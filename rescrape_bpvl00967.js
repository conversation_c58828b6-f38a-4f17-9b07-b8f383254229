require('dotenv').config();
const { Pool } = require('pg');

async function rescrapeBPVL00967() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🔄 RE-SCRAPING BPVL00967 WITH FIXED CSS PATTERNS...');
    
    // First, delete the existing property
    console.log('🗑️  Deleting existing BPVL00967...');
    const deleteResult = await pool.query(`
      DELETE FROM property 
      WHERE external_id = 'BPVL00967'
    `);
    console.log(`✅ Deleted ${deleteResult.rowCount} existing properties`);
    
    // Add it back to the scraping queue
    console.log('📝 Adding BPVL00967 back to scraping queue...');
    const insertResult = await pool.query(`
      INSERT INTO scraping_queue (source_id, url, priority, created_at)
      VALUES ('betterplace', 'https://betterplace.cc/buy/properties/BPVL00967', 1, NOW())
      ON CONFLICT (source_id, url) DO UPDATE SET
        priority = 1,
        created_at = NOW()
    `);
    console.log('✅ Added to scraping queue with high priority');
    
    console.log('\n🚀 Now start the queue processor to re-scrape:');
    console.log('   node scripts/start_queue_processor.js');
    console.log('\n🔍 Expected result: 5 bedrooms, 5 bathrooms (instead of 3/4)');
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

rescrapeBPVL00967();
