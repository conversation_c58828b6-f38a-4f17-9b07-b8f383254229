// Debug why bathroom extraction returns 20 instead of 2
const testMarkdown = `Online

[WhatsApp](https://wa.me/6282194359401?text=Hi%20Bali%20Home%20Immo%20Team%2C%20%0AI%E2%80%99m%20reaching%20out%20through%20your%20website%20homepage%20regarding%20the%20following%20inquiry%3A "WhatsApp")[Messenger](https://m.me/BaliHomeImmo "Messenger")[Telegram](https://t.me/Balihomeimmo "Telegram")

# Luxury 2 Bedroom Penthouse for rent in Batu Belig - BHI1403C

68.500.000
/month

|     |     |     |
| --- | --- | --- |
| Bedroom | : | 2 |
| Bathroom | : | 2 |
| Land Size | : | 158 m² |

### Indoor

|     |     |     |
| --- | --- | --- |
| Living room | : | Enclosed |
| Dinning room | : | Enclosed |
| Kitchen | : | Enclosed |
| Bedroom | : | 2 |
| Bathroom | : | 2 |
| Ensuite Bathroom | : | 2 |

Petitenget / Batu Belig
Batu Belig`;

console.log('🔍 DEBUGGING BATHROOM "20" PROBLEM');
console.log('='.repeat(50));

// Test all bathroom patterns from the Bali Home Immo mapper
const bathroomPatterns = [
  { name: 'Standard patterns', pattern: /(\d{1,2})\s*(?:bath|bathroom|kamar mandi)s?(?!\d)/gi },
  { name: 'Colon/space patterns', pattern: /(?:bath|bathroom)s?[:\s]+(\d{1,2})(?!\d)/gi },
  { name: 'Abbreviation patterns (FIXED)', pattern: /(?:^|[^a-zA-Z])(\d{1,2})\s*BA(?!\w)/gi },
  { name: 'Property details patterns', pattern: /[-•]\s*(\d{1,2})\s*(?:bath|bathroom)s?/gi },
  { name: 'Slash patterns', pattern: /\d+\s*(?:bed|bedroom|BR)s?[\/\s]*(\d{1,2})\s*(?:bath|bathroom|BA)s?/gi },
  { name: 'Indonesian patterns', pattern: /(\d{1,2})\s*kamar\s*mandi/gi },
  { name: 'Table/list patterns (FIXED)', pattern: /(?:bath|bathroom)s?\s*[|:\s]+(\d{1,2})/gi }
];

console.log('Testing ALL MATCHES (global flag) for each pattern:');

bathroomPatterns.forEach(({ name, pattern }) => {
  const matches = [...testMarkdown.matchAll(pattern)];
  if (matches.length > 0) {
    console.log(`✅ ${name}: Found ${matches.length} matches`);
    matches.forEach((match, i) => {
      console.log(`   ${i + 1}. "${match[0]}" → ${match[1]}`);
      
      // Show context
      const index = match.index;
      const context = testMarkdown.substring(Math.max(0, index - 20), index + match[0].length + 20);
      console.log(`      Context: "...${context}..."`);
    });
    
    // Show what happens if we concatenate all matches
    const allNumbers = matches.map(m => m[1]).join('');
    console.log(`   Combined: "${allNumbers}"`);
    
  } else {
    console.log(`❌ ${name}: No matches`);
  }
  console.log('');
});

// Test the exact logic from the Bali Home Immo mapper (first match only)
console.log('🔍 TESTING EXACT MAPPER LOGIC (first match only):');

const bathroomMatch =
  testMarkdown.match(/(\d{1,2})\s*(?:bath|bathroom|kamar mandi)s?(?!\d)/i) ||
  testMarkdown.match(/(?:bath|bathroom)s?[:\s]+(\d{1,2})(?!\d)/i) ||
  testMarkdown.match(/(?:^|[^a-zA-Z])(\d{1,2})\s*BA(?!\w)/i) ||
  testMarkdown.match(/[-•]\s*(\d{1,2})\s*(?:bath|bathroom)s?/i) ||
  testMarkdown.match(/\d+\s*(?:bed|bedroom|BR)s?[\/\s]*(\d{1,2})\s*(?:bath|bathroom|BA)s?/i) ||
  testMarkdown.match(/(\d{1,2})\s*kamar\s*mandi/i) ||
  testMarkdown.match(/(?:bath|bathroom)s?\s*[|:\s]+(\d{1,2})/i);

console.log('Bathroom match result:', bathroomMatch);
if (bathroomMatch) {
  console.log('Full match:', bathroomMatch[0]);
  console.log('Captured number:', bathroomMatch[1]);
  console.log('Parsed integer:', parseInt(bathroomMatch[1]));
  
  const bathrooms = parseInt(bathroomMatch[1]);
  console.log('Final bathroom count:', bathrooms);
  
  // Test validation
  if (bathrooms && (bathrooms > 15 || bathrooms < 1)) {
    console.log(`⚠️ Suspicious bathroom count: ${bathrooms}, would be set to null`);
  } else {
    console.log(`✅ Valid bathroom count: ${bathrooms}`);
  }
} else {
  console.log('❌ No bathroom match found');
}
