require('dotenv').config();
const { Pool } = require('pg');
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function compareRealVsDatabase() {
  console.log('🔍 REAL vs DATABASE COMPARISON');
  console.log('='.repeat(50));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get a few recent properties
    const result = await pool.query(`
      SELECT 
        external_id,
        title,
        bedrooms,
        bathrooms,
        source_url
      FROM property 
      WHERE bedrooms IS NOT NULL 
        AND bathrooms IS NOT NULL
        AND source_url LIKE '%betterplace.cc%'
      ORDER BY created_at DESC
      LIMIT 3
    `);
    
    console.log(`📊 Testing ${result.rows.length} properties:\n`);
    
    for (const property of result.rows) {
      console.log(`🏠 TESTING ${property.external_id}:`);
      console.log(`📍 URL: ${property.source_url}`);
      console.log(`💾 Database: ${property.bedrooms} bed / ${property.bathrooms} bath`);
      
      try {
        console.log('🔄 Re-scraping with current mapper...');
        
        // Use the actual scraper to get fresh data
        const scrapingResult = await runExtractBatch('betterplace', [property.source_url], {});
        
        if (scrapingResult && scrapingResult.extractedData && scrapingResult.extractedData.length > 0) {
          const freshData = scrapingResult.extractedData[0];
          
          console.log(`🆕 Fresh scrape: ${freshData.bedrooms} bed / ${freshData.bathrooms} bath`);
          
          // Compare
          const bedroomMatch = freshData.bedrooms === property.bedrooms;
          const bathroomMatch = freshData.bathrooms === property.bathrooms;
          
          console.log(`📊 COMPARISON:`);
          console.log(`   Bedrooms: ${property.bedrooms} (DB) vs ${freshData.bedrooms} (Fresh) ${bedroomMatch ? '✅' : '❌'}`);
          console.log(`   Bathrooms: ${property.bathrooms} (DB) vs ${freshData.bathrooms} (Fresh) ${bathroomMatch ? '✅' : '❌'}`);
          
          if (!bedroomMatch || !bathroomMatch) {
            console.log(`❌ MISMATCH DETECTED!`);
            console.log(`   Title: ${freshData.title}`);
            console.log(`   Fresh price: ${freshData.price}`);
          } else {
            console.log(`✅ Perfect match!`);
          }
          
        } else {
          console.log('❌ Fresh scraping failed');
        }
        
      } catch (error) {
        console.error(`❌ Error scraping ${property.external_id}: ${error.message}`);
      }
      
      console.log('\n' + '-'.repeat(40) + '\n');
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

compareRealVsDatabase();
