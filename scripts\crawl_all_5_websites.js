// Crawl all 5 websites to populate discovered_urls and scraping_queue
require('dotenv').config();
const { SmartCrawler } = require('../scrape_worker/smart_crawler');
const { db, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function crawlAll5Websites() {
  console.log('🕷️  Crawling All 5 Websites to Populate Queue\n');
  
  const websites = [
    { id: 'betterplace', name: 'BetterPlace' },
    { id: 'bali_home_immo', name: 'Bali Home Immo' },
    { id: 'bali_villa_realty', name: 'Bali Villa Realty' },
    { id: 'villabalisale.com', name: 'Villa Bali Sale' },
    { id: 'bali_coconut_living', name: 'Bali Coconut Living' }
  ];
  
  const crawler = new SmartCrawler();
  const results = {};
  
  try {
    // Show initial status
    console.log('📊 Initial Queue Status:');
    const initialUrls = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls`);
    const initialQueue = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue`);
    console.log(`   🔗 Discovered URLs: ${initialUrls[0]?.count || 0}`);
    console.log(`   📋 Scraping Queue: ${initialQueue[0]?.count || 0}\n`);
    
    // Crawl each website
    for (const website of websites) {
      console.log(`${'='.repeat(80)}`);
      console.log(`🌐 Crawling ${website.name} (${website.id})`);
      console.log(`${'='.repeat(80)}`);
      
      try {
        const result = await crawler.startWebsiteCrawl(website.id);
        
        if (result) {
          console.log(`✅ ${website.name} crawled successfully:`);
          console.log(`   📊 Type: ${result.type}`);
          console.log(`   🔍 Total discovered: ${result.totalDiscovered || 0}`);
          console.log(`   ✨ New URLs added: ${result.newUrls || 0}`);
          console.log(`   🗺️  Sitemaps processed: ${result.sitemapUrls || 0}`);
          
          results[website.id] = {
            name: website.name,
            success: true,
            type: result.type,
            totalDiscovered: result.totalDiscovered || 0,
            newUrls: result.newUrls || 0,
            sitemapUrls: result.sitemapUrls || 0
          };
        } else {
          console.log(`⏭️  ${website.name} skipped (not due for processing)`);
          results[website.id] = {
            name: website.name,
            success: false,
            reason: 'Skipped - not due for processing'
          };
        }
        
      } catch (error) {
        console.error(`❌ ${website.name} failed:`, error.message);
        results[website.id] = {
          name: website.name,
          success: false,
          error: error.message
        };
      }
      
      // Small delay between websites
      if (website !== websites[websites.length - 1]) {
        console.log('\n⏳ Waiting 10 seconds before next website...');
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
    
    // Show final status
    console.log(`\n${'='.repeat(80)}`);
    console.log('📊 FINAL CRAWLING RESULTS');
    console.log(`${'='.repeat(80)}`);
    
    const finalUrls = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls`);
    const finalQueue = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue`);
    
    console.log(`\n📈 Database Status After Crawling:`);
    console.log(`   🔗 Discovered URLs: ${finalUrls[0]?.count || 0} (was ${initialUrls[0]?.count || 0})`);
    console.log(`   📋 Scraping Queue: ${finalQueue[0]?.count || 0} (was ${initialQueue[0]?.count || 0})`);
    
    const newDiscovered = (finalUrls[0]?.count || 0) - (initialUrls[0]?.count || 0);
    const newQueued = (finalQueue[0]?.count || 0) - (initialQueue[0]?.count || 0);
    
    console.log(`\n✨ New URLs Added:`);
    console.log(`   🔍 Discovered: +${newDiscovered}`);
    console.log(`   📋 Queued: +${newQueued}`);
    
    // Show results by website
    console.log(`\n🌐 Results by Website:`);
    let successCount = 0;
    let totalNewUrls = 0;
    
    Object.values(results).forEach(result => {
      if (result.success) {
        successCount++;
        totalNewUrls += result.newUrls || 0;
        console.log(`   ✅ ${result.name}: ${result.newUrls || 0} new URLs (${result.totalDiscovered || 0} total discovered)`);
      } else {
        console.log(`   ❌ ${result.name}: ${result.error || result.reason || 'Failed'}`);
      }
    });
    
    console.log(`\n🎯 Summary:`);
    console.log(`   ✅ Successful websites: ${successCount}/${websites.length}`);
    console.log(`   📊 Total new URLs: ${totalNewUrls}`);
    
    // Show queue status by website
    console.log(`\n📋 Queue Status by Website:`);
    const queueStats = await db.execute(sql`
      SELECT 
        website_id,
        COUNT(*) as count,
        COUNT(*) FILTER (WHERE status = 'pending') as pending,
        COUNT(*) FILTER (WHERE status = 'completed') as completed,
        COUNT(*) FILTER (WHERE status = 'failed') as failed
      FROM scraping_queue 
      GROUP BY website_id
      ORDER BY website_id
    `);
    
    if (queueStats.rows && queueStats.rows.length > 0) {
      queueStats.rows.forEach(stat => {
        console.log(`   🌐 ${stat.website_id}: ${stat.count} total (${stat.pending} pending, ${stat.completed} completed, ${stat.failed} failed)`);
      });
    } else {
      console.log('   📭 No URLs in queue yet');
    }
    
    // Performance evaluation
    if (successCount === websites.length && totalNewUrls > 0) {
      console.log('\n🏆 EXCELLENT! All websites crawled successfully with URLs discovered!');
      console.log('🚀 Ready to run property scraping test!');
      console.log('\n💡 Next Steps:');
      console.log('   1. Run: node scripts/test_all_5_websites_ownership.js');
      console.log('   2. This will now find URLs in the queue and scrape properties');
    } else if (successCount >= 3) {
      console.log('\n✅ GOOD! Most websites crawled successfully.');
      console.log('⚠️  Some websites may need manual URL addition or configuration fixes.');
    } else {
      console.log('\n⚠️  NEEDS ATTENTION! Several websites failed to crawl.');
      console.log('🔧 Check website configurations and sitemap URLs.');
    }
    
    console.log('\n🎉 All 5 websites crawling completed!');
    
  } catch (error) {
    console.error('❌ Crawling failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the crawling
crawlAll5Websites()
  .then(() => {
    console.log('✅ All websites crawling completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Crawling failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
