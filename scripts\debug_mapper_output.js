// Debug what the mapper actually outputs
require('dotenv').config();

async function debugMapperOutput() {
  console.log('🔍 Debugging Villa Bali Sale Mapper Output\n');
  
  // Use the REAL JSON data we extracted
  const realJsonData = {
    "propertyID": "CVS1355A",
    "ownershipType": "Leasehold / 27 years",
    "landSize": "2.99 Are",
    "buildingSize": "122 m2",
    "parking": "Parking / Carport",
    "yearBuilt": null,
    "description": "This Off-Plan modern tropical real estate is located in a peaceful area of Amed, on the east side of Bali. This villa features 2 spacious, modern and stylish en-suite bedrooms with classy bathrooms. It is fully furnished in a great taste with expensive furniture and decoration that provides great luxury and comfort.\n\nThis property generously offers living area with huge window to allow more light, proper dining space, kitchen, storage room, beautiful swimming pool, electrical capacity and a beautiful garden in the house.\n\nThis is one of the best opportunity available for sale in Bali right now. Available on leasehold. 7 minutes away from the beach, 5 minutes to the market."
  };
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/amed/homey-off-plan-villa-in-amed-east-bali-for-sale';
  
  const rawData = {
    url: testUrl,
    json: realJsonData,
    markdown: null,
    html: null
  };
  
  try {
    const { mapVillaBaliSale } = require('../scrape_worker/mappers');
    
    console.log('🔄 Testing mapper with REAL JSON data...');
    const mapped = await mapVillaBaliSale(rawData);
    
    console.log('\n📊 COMPLETE Mapper Output:');
    console.log('='.repeat(60));
    console.log(JSON.stringify(mapped, null, 2));
    
    console.log('\n🎯 KEY FIELDS Analysis:');
    console.log('='.repeat(60));
    console.log(`Title: ${mapped.title}`);
    console.log(`Description: ${mapped.description ? 'PRESENT (' + mapped.description.length + ' chars)' : 'MISSING'}`);
    console.log(`Size (sqft): ${mapped.size_sqft || 'MISSING'}`);
    console.log(`Lot Size (sqft): ${mapped.lot_size_sqft || 'MISSING'}`);
    console.log(`Parking: ${mapped.parking_spaces || 'MISSING'}`);
    console.log(`External ID: ${mapped.media?.external_id || 'MISSING'}`);
    console.log(`Ownership Type: ${mapped.amenities?.ownership_type || 'MISSING'}`);
    
    console.log('\n🔍 Raw Amenities Object:');
    console.log(JSON.stringify(mapped.amenities, null, 2));
    
    console.log('\n🔍 Raw Media Object:');
    console.log(JSON.stringify(mapped.media, null, 2));
    
    // Test individual field extraction
    console.log('\n🧪 Individual Field Tests:');
    console.log('='.repeat(60));
    
    console.log(`Input propertyID: "${realJsonData.propertyID}"`);
    console.log(`Input ownershipType: "${realJsonData.ownershipType}"`);
    console.log(`Input landSize: "${realJsonData.landSize}"`);
    console.log(`Input buildingSize: "${realJsonData.buildingSize}"`);
    console.log(`Input description length: ${realJsonData.description.length} chars`);
    
    // Test size conversion
    if (realJsonData.buildingSize) {
      const sizeMatch = realJsonData.buildingSize.match(/(\d+)\s*m2/i);
      if (sizeMatch) {
        const sqm = parseInt(sizeMatch[1]);
        const sqft = sqm * 10.764;
        console.log(`Building size conversion: ${sqm}m2 → ${Math.round(sqft)} sqft`);
      }
    }
    
    // Test land size conversion
    if (realJsonData.landSize) {
      const landMatch = realJsonData.landSize.match(/([0-9.]+)\s*are/i);
      if (landMatch) {
        const are = parseFloat(landMatch[1]);
        const sqft = are * 1076.4;
        console.log(`Land size conversion: ${are} Are → ${Math.round(sqft)} sqft`);
      }
    }
    
  } catch (error) {
    console.error('❌ Mapper Error:', error.message);
    console.error(error.stack);
  }
}

debugMapperOutput();
