require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testFix() {
  console.log('🔧 TESTING THE FIX');
  console.log('='.repeat(40));
  
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL00910';
  
  console.log(`📍 Testing: ${testUrl}`);
  console.log(`🎯 Expected: 3 bedrooms, 4 bathrooms`);
  
  try {
    const result = await runExtractBatch('betterplace', [testUrl], {});
    
    if (result && result.extractedData && result.extractedData.length > 0) {
      const data = result.extractedData[0];
      
      console.log(`\n✅ EXTRACTION RESULT:`);
      console.log(`   Bedrooms: ${data.bedrooms} ${data.bedrooms === 3 ? '✅' : '❌'}`);
      console.log(`   Bathrooms: ${data.bathrooms} ${data.bathrooms === 4 ? '✅' : '❌'}`);
      console.log(`   Title: ${data.title}`);
      
      if (data.bedrooms === 3 && data.bathrooms === 4) {
        console.log(`\n🎉 SUCCESS! The fix works perfectly!`);
      } else {
        console.log(`\n❌ Still not working correctly`);
      }
      
    } else {
      console.log('❌ No data extracted');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testFix();
