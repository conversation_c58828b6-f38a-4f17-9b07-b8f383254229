// Direct test of the detectBetterPlacePropertyType function
function detectBetterPlacePropertyType(property_id, title, markdown) {
  const id = property_id?.toUpperCase() || '';
  const titleText = (title || '').toLowerCase();

  console.log(`🔍 Testing detectBetterPlacePropertyType:`);
  console.log(`   ID: ${id}`);
  console.log(`   Title: "${title}"`);
  console.log(`   Title text: "${titleText}"`);

  // Content based detection FIRST (more accurate for edge cases) - USE TITLE ONLY
  if (titleText.includes('apartment') || titleText.includes('condo')) {
    console.log(`   ✅ Content match: apartment/condo → apartment`);
    return 'apartment';
  }
  if (titleText.includes('land') || titleText.includes('plot') || titleText.includes('lot')) {
    console.log(`   ✅ Content match: land/plot/lot → land`);
    return 'land';
  }
  if (titleText.includes('hotel') || titleText.includes('resort')) {
    console.log(`   ✅ Content match: hotel/resort → hotel`);
    return 'hotel';
  }
  if (titleText.includes('villa') || titleText.includes('house')) {
    console.log(`   ✅ Content match: villa/house → villa`);
    return 'villa';
  }

  console.log(`   🔍 No content match, checking ID patterns...`);

  // Property ID based detection as fallback (when content is unclear)
  if (id.startsWith('BPVL')) {
    console.log(`   📋 ID match: BPVL → villa`);
    return 'villa';
  }
  if (id.startsWith('BPVF')) {
    console.log(`   📋 ID match: BPVF → villa`);
    return 'villa';
  }
  if (id.startsWith('BPHL')) {
    console.log(`   📋 ID match: BPHL → hotel`);
    return 'hotel';
  }
  if (id.startsWith('BPAP')) {
    console.log(`   📋 ID match: BPAP → apartment`);
    return 'apartment';
  }
  if (id.startsWith('BPLD') || id.startsWith('BPLF')) {
    console.log(`   📋 ID match: BPLD/BPLF → land`);
    return 'land';
  }
  if (id.startsWith('BPLL')) {
    console.log(`   📋 ID match: BPLL → land`);
    return 'land';
  }
  if (id.startsWith('BPFL')) {
    console.log(`   📋 ID match: BPFL → land`);
    return 'land';
  }

  console.log(`   🏠 Default fallback → villa`);
  return 'villa';
}

// Test the problematic cases
const testCases = [
  {
    id: 'BPLL00727',
    title: 'Seaside Dreams Unleashed: Exclusive Leasehold Land in Pangkung Tibah – Kedungu Awaits Your Vision',
    expected: 'land'
  },
  {
    id: 'BPVF00745', 
    title: 'Spacious and Modern Living, 3 Bedroom Brand New Villa in Cepaka',
    expected: 'villa'
  },
  {
    id: 'BPHL00746',
    title: 'Live the High Life: Elegant and Furnished Leasehold 1-Bed Panoramic Apartment in Canggu – Lima Beach',
    expected: 'apartment'
  }
];

console.log('🧪 TESTING detectBetterPlacePropertyType FUNCTION');
console.log('='.repeat(80));

testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. TESTING: ${testCase.id}`);
  console.log('='.repeat(60));
  
  const result = detectBetterPlacePropertyType(testCase.id, testCase.title, '');
  
  console.log(`\n📊 RESULT: ${result}`);
  console.log(`   Expected: ${testCase.expected}`);
  console.log(`   Status: ${result === testCase.expected ? '✅ CORRECT' : '❌ WRONG'}`);
});
