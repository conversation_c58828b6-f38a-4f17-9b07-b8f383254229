// Test the exact filtering logic on the specific line we found
const testLine = "Imagine stepping into one of Bali's most promising opportunities for real estate development, where an expansive 4,230 sqm freehold plot awaits in the vibrant heart of East Bali. Nestled in Bubug Karangasem, a region buzzing with potential, this land is priced at IDR 350,000,000 per are, offering an exceptional chance to tap into the island's flourishing tourism and steady investment landscape. Just five minutes from the famous Virgin Beach, this spot lies in Bukit Asah Karangasem, a paradise brimming with possibilities. The land's unique position makes it an ideal site for luxury retreats, beachfront villas, or even an exclusive boutique resort. Lush natural beauty encircles the area, along with popular dining spots and attractions visitors can't resist. If you've been dreaming about a real estate venture that offers breathtaking views and strategic growth potential, this property is a rare find in Bali.";

console.log('🧪 TESTING FILTER LOGIC ON SPECIFIC LINE\n');
console.log(`Line length: ${testLine.length} characters`);
console.log(`Line content: ${testLine.substring(0, 100)}...\n`);

const line = testLine.trim();
const lowerLine = line.toLowerCase();

console.log('FILTER TESTS:');

// Test 1: Length check
console.log(`1. Length >= 15: ${line.length >= 15 ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Contact/technical content
const contactFilters = [
  'whatsapp', 'wa.me', 'wp-content', '_next/image', 'digitaloceanspaces',
  'contact us', 'phone:', 'email:', 'call us', 'reach out', 'get in touch',
  'schedule', 'consultation', 'booking', 'reserve', 'inquiry', 'enquiry',
  'online', 'website', 'click', 'visit us', 'visit our', 'browse', 'download', 'subscribe',
  'newsletter', 'follow us', 'social media', 'facebook', 'instagram',
  'twitter', 'linkedin', 'youtube', 'telegram', 'free consultation',
  'free advice', 'no obligation', 'terms and conditions', 'privacy policy',
  'cookie policy', 'disclaimer', 'answer few questions', 'we will offer',
  'real estate solutions', 'fill out', 'form', 'submit', 'send us',
  'contact form', 'get quote', 'request info', 'more information',
  'call to action', 'cta', 'button', 'link'
];

let contactFilterTriggered = false;
for (const filter of contactFilters) {
  if (lowerLine.includes(filter)) {
    console.log(`2. Contact filter "${filter}": ❌ TRIGGERED`);
    contactFilterTriggered = true;
    break;
  }
}
if (!contactFilterTriggered) {
  console.log('2. Contact filters: ✅ PASS');
}

// Test 3: Markdown/HTML markup
const markupChecks = [
  line.includes('[!['),
  line.includes('](http'),
  line.includes('<img'),
  line.includes('<a href'),
  line.includes('!['),
  line.startsWith('#'),
  line.startsWith('*'),
  line.startsWith('-'),
  line.startsWith('|'),
  line.startsWith('>')
];

const markupTriggered = markupChecks.some(check => check);
console.log(`3. Markup filters: ${markupTriggered ? '❌ TRIGGERED' : '✅ PASS'}`);

// Test 4: Currency/pricing (NEW LOGIC)
const currencyFilters = ['usd', 'idr', 'aud', 'eur', 'price', 'cost', '$', 'rp ', '€', '£'];
let currencyFilterTriggered = false;
if (line.length < 100) {
  for (const filter of currencyFilters) {
    if (lowerLine.includes(filter)) {
      console.log(`4. Currency filter "${filter}" (line < 100): ❌ TRIGGERED`);
      currencyFilterTriggered = true;
      break;
    }
  }
} else {
  console.log(`4. Currency filters (line >= 100): ✅ PASS (line is ${line.length} chars)`);
}

// Test 5: Property specifications (NEW LOGIC)
const specFilters = ['bedroom', 'bathroom', 'sqm', 'm2', 'm²', 'parking', 'year built', 'ownership', 'built:', 'size:'];
let specFilterTriggered = false;
if (line.length < 80) {
  for (const filter of specFilters) {
    if (lowerLine.includes(filter)) {
      console.log(`5. Spec filter "${filter}" (line < 80): ❌ TRIGGERED`);
      specFilterTriggered = true;
      break;
    }
  }
} else {
  console.log(`5. Spec filters (line >= 80): ✅ PASS (line is ${line.length} chars)`);
}

// Test 6: Freehold/leasehold short lines
const freeholdCheck = line.length < 50 && (lowerLine.includes('leasehold') || lowerLine.includes('freehold'));
console.log(`6. Freehold/leasehold short line: ${freeholdCheck ? '❌ TRIGGERED' : '✅ PASS'} (line is ${line.length} chars)`);

// Final result
const shouldPass = line.length >= 15 && 
                   !contactFilterTriggered && 
                   !markupTriggered && 
                   !currencyFilterTriggered && 
                   !specFilterTriggered && 
                   !freeholdCheck;

console.log(`\n🎯 FINAL RESULT: ${shouldPass ? '✅ LINE SHOULD PASS FILTERS' : '❌ LINE SHOULD BE FILTERED OUT'}`);

if (shouldPass) {
  console.log('\n✨ This line should be extracted as the description!');
} else {
  console.log('\n⚠️  This line is being incorrectly filtered out.');
}

process.exit(0);
