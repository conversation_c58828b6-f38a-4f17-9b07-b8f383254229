require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');

async function testRescrape() {
  console.log('🧪 TESTING RE-SCRAPE OF BPHL00994...');
  
  const queueManager = new QueueManager();
  
  try {
    // Force re-scrape of BPHL00994
    const testUrl = 'https://betterplace.cc/buy/properties/BPHL00994';
    
    console.log('🔄 Force re-scraping BPHL00994...');
    
    // Add to queue with force flag
    await queueManager.addUrls('betterplace', [testUrl], { force: true });
    
    console.log('✅ Added to queue for re-scraping');
    console.log('🚀 Starting processing...');
    
    // Process the single URL
    await queueManager.processQueue();
    
    console.log('✅ Re-scrape completed!');
    
  } catch (error) {
    console.error('❌ ERROR:', error.message);
  }
}

testRescrape();
