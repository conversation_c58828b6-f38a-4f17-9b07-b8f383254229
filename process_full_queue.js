// Process the full scraping queue with the fixed system
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, testConnection } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function processFullQueue() {
  console.log('🚀 STARTING FULL QUEUE PROCESSING');
  console.log('=' .repeat(60));
  
  try {
    // Test database connection
    console.log('🔍 Testing database connection...');
    const connected = await testConnection();
    if (!connected) {
      console.log('❌ Database connection failed');
      return;
    }
    
    // Show initial status
    console.log('\n📊 Initial Status:');
    const initialCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`Properties in database: ${initialCount[0].count}`);
    
    // Check queue status
    const queueStatus = await db.execute(sql`
      SELECT 
        website_id,
        status,
        COUNT(*) as count
      FROM scraping_queue 
      GROUP BY website_id, status
      ORDER BY website_id, status
    `);
    
    console.log('\n📋 Queue Status:');
    console.table(queueStatus);
    
    // Get pending URLs count per website
    const pendingCounts = await db.execute(sql`
      SELECT 
        website_id,
        COUNT(*) as pending_count
      FROM scraping_queue 
      WHERE status = 'pending'
      GROUP BY website_id
      ORDER BY pending_count DESC
    `);
    
    console.log('\n🎯 Pending URLs by Website:');
    pendingCounts.forEach(row => {
      console.log(`   ${row.website_id}: ${row.pending_count} URLs`);
    });
    
    // Initialize queue manager
    const queueManager = new QueueManager();
    
    // Process each website with pending URLs
    for (const row of pendingCounts) {
      const websiteId = row.website_id;
      const pendingCount = parseInt(row.pending_count);
      
      if (pendingCount === 0) continue;
      
      console.log(`\n🌐 Processing ${websiteId} (${pendingCount} URLs)...`);
      
      // Process in batches to avoid overwhelming the system
      const batchSize = 50; // Process 50 URLs at a time
      let processed = 0;
      let totalSuccess = 0;
      let totalFailed = 0;
      
      while (processed < pendingCount) {
        const remaining = pendingCount - processed;
        const currentBatchSize = Math.min(batchSize, remaining);
        
        console.log(`\n📦 Processing batch ${Math.floor(processed / batchSize) + 1} (${currentBatchSize} URLs, ${remaining} remaining)...`);
        
        try {
          // Get URLs for this batch
          const batchUrls = await db.execute(sql`
            SELECT url 
            FROM scraping_queue 
            WHERE website_id = ${websiteId} 
            AND status = 'pending'
            ORDER BY created_at ASC
            LIMIT ${currentBatchSize}
          `);
          
          if (batchUrls.length === 0) {
            console.log('   ⚠️ No more pending URLs found');
            break;
          }
          
          const urls = batchUrls.map(row => row.url);
          
          // Process this batch
          const results = await queueManager.processSpecificUrls(websiteId, urls);
          
          // Count results
          const batchSuccess = results.filter(r => r.ok).length;
          const batchFailed = results.filter(r => !r.ok).length;
          
          totalSuccess += batchSuccess;
          totalFailed += batchFailed;
          processed += urls.length;
          
          console.log(`   ✅ Batch complete: ${batchSuccess} success, ${batchFailed} failed`);
          console.log(`   📊 Total progress: ${processed}/${pendingCount} (${((processed/pendingCount)*100).toFixed(1)}%)`);
          
          // Update queue status for processed URLs
          for (let i = 0; i < urls.length; i++) {
            const url = urls[i];
            const result = results[i];
            const newStatus = result.ok ? 'completed' : 'failed';
            
            await db.execute(sql`
              UPDATE scraping_queue 
              SET status = ${newStatus}, 
                  processed_at = NOW(),
                  error_message = ${result.error || null}
              WHERE website_id = ${websiteId} 
              AND url = ${url}
            `);
          }
          
          // Brief pause between batches to avoid overwhelming APIs
          if (processed < pendingCount) {
            console.log('   ⏸️ Pausing 10 seconds between batches...');
            await new Promise(resolve => setTimeout(resolve, 10000));
          }
          
        } catch (error) {
          console.error(`   ❌ Batch processing error:`, error.message);
          // Continue with next batch
          processed += currentBatchSize;
        }
      }
      
      console.log(`\n🎉 ${websiteId} complete: ${totalSuccess} success, ${totalFailed} failed`);
      console.log(`   Success rate: ${totalSuccess > 0 ? ((totalSuccess/(totalSuccess+totalFailed))*100).toFixed(1) : 0}%`);
    }
    
    // Final status report
    console.log('\n📊 FINAL RESULTS:');
    console.log('=' .repeat(60));
    
    const finalCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    const newProperties = finalCount[0].count - initialCount[0].count;
    console.log(`Properties in database: ${finalCount[0].count} (+${newProperties} new)`);
    
    // Updated queue status
    const finalQueueStatus = await db.execute(sql`
      SELECT 
        website_id,
        status,
        COUNT(*) as count
      FROM scraping_queue 
      GROUP BY website_id, status
      ORDER BY website_id, status
    `);
    
    console.log('\n📋 Final Queue Status:');
    console.table(finalQueueStatus);
    
    // Success statistics
    const successStats = await db.execute(sql`
      SELECT 
        website_id,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(*) as total
      FROM scraping_queue 
      GROUP BY website_id
      ORDER BY completed DESC
    `);
    
    console.log('\n📈 Success Statistics:');
    console.table(successStats.map(stat => ({
      Website: stat.website_id,
      Completed: stat.completed,
      Failed: stat.failed,
      Pending: stat.pending,
      Total: stat.total,
      'Success Rate': stat.completed > 0 ? `${((stat.completed/(stat.completed + stat.failed))*100).toFixed(1)}%` : '0%'
    })));
    
    console.log('\n🎉 FULL QUEUE PROCESSING COMPLETED!');
    
  } catch (error) {
    console.error('❌ Queue processing failed:', error);
  }
  
  process.exit(0);
}

processFullQueue();
