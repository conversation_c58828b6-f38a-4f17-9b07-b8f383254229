// Analyze complete sitemaps to understand real URL patterns
require('dotenv').config();
const { SitemapParser } = require('../scrape_worker/sitemap_parser');
const postgres = require('postgres');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

async function analyzeFullSitemaps() {
  console.log('🔍 Analyzing Complete Sitemaps for Real URL Patterns\n');
  
  try {
    const parser = new SitemapParser();
    
    // Analyze each website's complete sitemap
    await analyzeBaliHomeImmoComplete(parser);
    await analyzeBaliVillaRealtyComplete(parser);
    await analyzeBetterPlaceComplete(parser);
    
    console.log('\n🎉 Complete sitemap analysis finished!');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    console.error(error.stack);
  } finally {
    await client.end();
  }
}

async function analyzeBaliHomeImmoComplete(parser) {
  console.log('🌐 BALI HOME IMMO - Complete Sitemap Analysis');
  console.log('='.repeat(60));
  
  try {
    // Parse the complete sitemap without any filters
    const config = {
      website_id: 'bali_home_immo',
      property_url_patterns: {
        patterns: [], // No patterns - get everything
        keywords: []
      }
    };
    
    const urls = await parser.parseSitemap('https://bali-home-immo.com/sitemap.xml', config);
    console.log(`📊 Total URLs in sitemap: ${urls.length}`);
    
    // Analyze URL patterns
    const urlPatterns = {};
    const pathAnalysis = {};
    
    urls.forEach(urlObj => {
      const url = urlObj.url;
      const path = new URL(url).pathname;
      
      // Count different path patterns
      const pathParts = path.split('/').filter(part => part.length > 0);
      const pathPattern = pathParts.map(part => {
        // Replace specific IDs/names with placeholders
        if (/^\d+$/.test(part)) return '[NUMBER]';
        if (part.length > 20) return '[LONG_NAME]';
        return part;
      }).join('/');
      
      pathAnalysis[pathPattern] = (pathAnalysis[pathPattern] || 0) + 1;
    });
    
    // Show top 20 most common patterns
    console.log('\n📋 Top 20 URL Patterns:');
    const sortedPatterns = Object.entries(pathAnalysis)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20);
    
    sortedPatterns.forEach(([pattern, count], i) => {
      console.log(`${i + 1}. /${pattern} (${count} URLs)`);
    });
    
    // Show sample URLs for top patterns
    console.log('\n🔍 Sample URLs for Top Patterns:');
    for (let i = 0; i < Math.min(5, sortedPatterns.length); i++) {
      const [pattern, count] = sortedPatterns[i];
      console.log(`\n📂 Pattern: /${pattern} (${count} URLs)`);
      
      // Find sample URLs matching this pattern
      const samples = urls.filter(urlObj => {
        const path = new URL(urlObj.url).pathname;
        const pathParts = path.split('/').filter(part => part.length > 0);
        const urlPattern = pathParts.map(part => {
          if (/^\d+$/.test(part)) return '[NUMBER]';
          if (part.length > 20) return '[LONG_NAME]';
          return part;
        }).join('/');
        return urlPattern === pattern;
      }).slice(0, 3);
      
      samples.forEach((sample, j) => {
        console.log(`   ${j + 1}. ${sample.url}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Bali Home Immo analysis failed:', error.message);
  }
}

async function analyzeBaliVillaRealtyComplete(parser) {
  console.log('\n🌐 BALI VILLA REALTY - Complete Sitemap Analysis');
  console.log('='.repeat(60));
  
  try {
    const config = {
      website_id: 'bali_villa_realty',
      property_url_patterns: {
        patterns: [],
        keywords: []
      }
    };
    
    // Parse both sitemaps
    const sitemap1 = await parser.parseSitemap('https://balivillarealty.com/property-sitemap.xml', config);
    const sitemap2 = await parser.parseSitemap('https://balivillarealty.com/property-sitemap2.xml', config);
    
    const allUrls = [...sitemap1, ...sitemap2];
    console.log(`📊 Total URLs in sitemaps: ${allUrls.length} (${sitemap1.length} + ${sitemap2.length})`);
    
    // Analyze patterns
    const pathAnalysis = {};
    
    allUrls.forEach(urlObj => {
      const url = urlObj.url;
      const path = new URL(url).pathname;
      const pathParts = path.split('/').filter(part => part.length > 0);
      const pathPattern = pathParts.map(part => {
        if (/^\d+$/.test(part)) return '[NUMBER]';
        if (part.length > 30) return '[LONG_NAME]';
        return part;
      }).join('/');
      
      pathAnalysis[pathPattern] = (pathAnalysis[pathPattern] || 0) + 1;
    });
    
    console.log('\n📋 Top 15 URL Patterns:');
    const sortedPatterns = Object.entries(pathAnalysis)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 15);
    
    sortedPatterns.forEach(([pattern, count], i) => {
      console.log(`${i + 1}. /${pattern} (${count} URLs)`);
    });
    
    // Show samples
    console.log('\n🔍 Sample URLs for Top Patterns:');
    for (let i = 0; i < Math.min(3, sortedPatterns.length); i++) {
      const [pattern, count] = sortedPatterns[i];
      console.log(`\n📂 Pattern: /${pattern} (${count} URLs)`);
      
      const samples = allUrls.filter(urlObj => {
        const path = new URL(urlObj.url).pathname;
        const pathParts = path.split('/').filter(part => part.length > 0);
        const urlPattern = pathParts.map(part => {
          if (/^\d+$/.test(part)) return '[NUMBER]';
          if (part.length > 30) return '[LONG_NAME]';
          return part;
        }).join('/');
        return urlPattern === pattern;
      }).slice(0, 3);
      
      samples.forEach((sample, j) => {
        console.log(`   ${j + 1}. ${sample.url}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Bali Villa Realty analysis failed:', error.message);
  }
}

async function analyzeBetterPlaceComplete(parser) {
  console.log('\n🌐 BETTERPLACE - Complete Sitemap Analysis');
  console.log('='.repeat(60));
  
  try {
    const config = {
      website_id: 'betterplace',
      property_url_patterns: {
        patterns: [],
        keywords: []
      },
      sitemap_filters: {
        include: ['real-estate', 'buy', 'rent'],
        exclude: ['blog', 'pages']
      }
    };
    
    const urls = await parser.parseSitemap('https://betterplace.cc/sitemap_index.xml', config);
    console.log(`📊 Total URLs in relevant sitemaps: ${urls.length}`);
    
    // Analyze patterns
    const pathAnalysis = {};
    
    urls.forEach(urlObj => {
      const url = urlObj.url;
      const path = new URL(url).pathname;
      const pathParts = path.split('/').filter(part => part.length > 0);
      const pathPattern = pathParts.map(part => {
        if (/^BPVL\d+$/.test(part)) return '[BPVL_ID]';
        if (/^BPVR\d+$/.test(part)) return '[BPVR_ID]';
        if (/^\d+$/.test(part)) return '[NUMBER]';
        if (part.length > 20) return '[LONG_NAME]';
        return part;
      }).join('/');
      
      pathAnalysis[pathPattern] = (pathAnalysis[pathPattern] || 0) + 1;
    });
    
    console.log('\n📋 Top 20 URL Patterns:');
    const sortedPatterns = Object.entries(pathAnalysis)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20);
    
    sortedPatterns.forEach(([pattern, count], i) => {
      console.log(`${i + 1}. /${pattern} (${count} URLs)`);
    });
    
    // Show samples
    console.log('\n🔍 Sample URLs for Top Patterns:');
    for (let i = 0; i < Math.min(5, sortedPatterns.length); i++) {
      const [pattern, count] = sortedPatterns[i];
      console.log(`\n📂 Pattern: /${pattern} (${count} URLs)`);
      
      const samples = urls.filter(urlObj => {
        const path = new URL(urlObj.url).pathname;
        const pathParts = path.split('/').filter(part => part.length > 0);
        const urlPattern = pathParts.map(part => {
          if (/^BPVL\d+$/.test(part)) return '[BPVL_ID]';
          if (/^BPVR\d+$/.test(part)) return '[BPVR_ID]';
          if (/^\d+$/.test(part)) return '[NUMBER]';
          if (part.length > 20) return '[LONG_NAME]';
          return part;
        }).join('/');
        return urlPattern === pattern;
      }).slice(0, 3);
      
      samples.forEach((sample, j) => {
        console.log(`   ${j + 1}. ${sample.url}`);
      });
    }
    
  } catch (error) {
    console.error('❌ BetterPlace analysis failed:', error.message);
  }
}

analyzeFullSitemaps();
