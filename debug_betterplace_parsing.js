const { mapBetterPlace } = require('./scrape_worker/mappers');

async function debugBetterPlaceParsing() {
  try {
    console.log('🔍 DEBUGGING BETTERPLACE PARSING...\n');

    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    console.log('URL:', testUrl);

    // 1. First scrape the raw data
    console.log('\n1. SCRAPING RAW DATA:');
    const scraped = await scrapeTestUrl(testUrl);
    
    if (scraped && scraped.success) {
      console.log('✅ Scraping successful');
      console.log('- Has JSON:', !!scraped.data?.json);
      console.log('- Has Markdown:', !!scraped.data?.markdown);
      console.log('- Markdown length:', scraped.data?.markdown?.length || 0);
      
      // Show first 500 chars of markdown
      if (scraped.data?.markdown) {
        console.log('\n📝 MARKDOWN PREVIEW (first 500 chars):');
        console.log(scraped.data.markdown.substring(0, 500));
        console.log('...\n');
      }
      
      // 2. Now test the mapper
      console.log('2. TESTING MAPPER:');
      const rawData = {
        url: testUrl,
        json: scraped.data?.json,
        markdown: scraped.data?.markdown,
        html: scraped.data?.html
      };
      
      const mapped = await mapBetterPlace(rawData);
      
      if (mapped) {
        console.log('✅ Mapping successful');
        console.log('- Title:', mapped.title);
        console.log('- Type:', mapped.type);
        console.log('- Category:', mapped.category);
        console.log('- Description length:', mapped.description?.length || 0);
        console.log('- Description:', mapped.description);
        console.log('- Amenities:', mapped.amenities);
        console.log('- Location:', mapped.location);
        console.log('- Price:', mapped.price);
        console.log('- Status:', mapped.status);
      } else {
        console.log('❌ Mapping failed - returned null');
      }
      
    } else {
      console.log('❌ Scraping failed');
      console.log('Error:', scraped?.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

// Make scrapeTestUrl available
async function scrapeTestUrl(url) {
  const { runExtractBatch } = require('./scrape_worker/run_batch');
  
  try {
    // Use the internal scraping function
    const results = await runExtractBatch('betterplace', [url], {});
    
    if (results && results.extractedData && results.extractedData.length > 0) {
      return {
        success: true,
        data: results.extractedData[0]
      };
    } else {
      return {
        success: false,
        error: 'No data extracted'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

debugBetterPlaceParsing();
