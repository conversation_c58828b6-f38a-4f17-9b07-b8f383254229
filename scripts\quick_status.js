// Quick database status check
require('dotenv').config();
const { db, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function quickStatus() {
  try {
    console.log('📊 Quick Database Status\n');
    
    // Overall counts
    const discoveredCount = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls`);
    const queueCount = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue`);
    
    console.log(`🔗 Total Discovered URLs: ${discoveredCount[0]?.count || 0}`);
    console.log(`📋 Total Scraping Queue: ${queueCount[0]?.count || 0}`);
    
    // By website
    const byWebsite = await db.execute(sql`
      SELECT 
        website_id,
        COUNT(*) as discovered,
        COUNT(*) FILTER (WHERE is_property_page = true) as properties
      FROM discovered_urls 
      GROUP BY website_id
      ORDER BY website_id
    `);
    
    console.log('\n📊 By Website:');
    byWebsite.forEach(row => {
      console.log(`   🌐 ${row.website_id}: ${row.discovered} discovered, ${row.properties} properties`);
    });
    
    // Queue by website
    const queueByWebsite = await db.execute(sql`
      SELECT website_id, COUNT(*) as count
      FROM scraping_queue 
      GROUP BY website_id
      ORDER BY website_id
    `);
    
    console.log('\n📋 Queue by Website:');
    queueByWebsite.forEach(row => {
      console.log(`   🌐 ${row.website_id}: ${row.count} in queue`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    closeConnection();
  }
}

quickStatus();
