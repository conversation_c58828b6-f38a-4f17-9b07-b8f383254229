// Debug Bali Villa Realty classification issue
require('dotenv').config();

// Import the classification function directly
function classifyProperty(title, description) {
  const text = `${title} ${description}`.toLowerCase();

  console.log(`🔍 Analyzing text: "${text}"`);
  console.log(`📝 Title: "${title}"`);
  console.log(`📝 Description: "${description || 'N/A'}"`);

  // Commercial property detection
  if (text.includes('shop') || text.includes('office') || text.includes('retail') ||
      text.includes('commercial') || text.includes('store') || text.includes('business') ||
      text.includes('warehouse') || text.includes('industrial')) {
    
    console.log('❌ COMMERCIAL detected due to keywords:', 
      text.match(/(shop|office|retail|commercial|store|business|warehouse|industrial)/g));
    
    if (text.includes('office')) return { category: 'COMMERCIAL', type: 'OFFICE' };
    if (text.includes('shop') || text.includes('retail') || text.includes('store')) return { category: 'COMMERCIAL', type: 'RETAIL' };
    if (text.includes('warehouse') || text.includes('storage')) return { category: 'COMMERCIAL', type: 'WAREHOUSE' };
    return { category: 'COMMERCIAL', type: 'OTHER' };
  }

  // Residential property detection
  if (text.includes('villa')) {
    console.log('✅ VILLA detected');
    return { category: 'RESIDENTIAL', type: 'VILLA' };
  }
  if (text.includes('apartment') || text.includes('apt')) {
    console.log('✅ APARTMENT detected');
    return { category: 'RESIDENTIAL', type: 'APARTMENT' };
  }
  if (text.includes('house') || text.includes('home')) {
    console.log('✅ HOUSE detected');
    return { category: 'RESIDENTIAL', type: 'HOUSE' };
  }
  if (text.includes('condo') || text.includes('condominium')) {
    console.log('✅ CONDO detected');
    return { category: 'RESIDENTIAL', type: 'CONDO' };
  }
  if (text.includes('townhouse') || text.includes('town house')) {
    console.log('✅ TOWNHOUSE detected');
    return { category: 'RESIDENTIAL', type: 'TOWNHOUSE' };
  }

  // Land detection
  if (text.includes('land') || text.includes('plot') || text.includes('lot') || text.includes('terrain')) {
    console.log('✅ LAND detected');
    return { category: 'LAND', type: 'LAND' };
  }

  // Industrial detection
  if (text.includes('factory') || text.includes('manufacturing') || text.includes('industrial')) {
    console.log('✅ INDUSTRIAL detected');
    return { category: 'INDUSTRIAL', type: 'OTHER' };
  }

  // Default to residential villa for Bali properties
  console.log('✅ DEFAULT: RESIDENTIAL VILLA');
  return { category: 'RESIDENTIAL', type: 'VILLA' };
}

function debugClassification() {
  console.log('🧪 Debugging Bali Villa Realty Classification\n');
  
  // Test the problematic property
  const testCases = [
    {
      title: 'Brand-new 1 Bedroom Villa for Sale Leasehold in Bali Tumbak Bayuh',
      description: '',
      expected: { category: 'RESIDENTIAL', type: 'VILLA' }
    },
    {
      title: 'Brand-new 1 Bedroom Villa for Sale Leasehold in Bali Tumbak Bayuh',
      description: 'Beautiful villa with modern amenities',
      expected: { category: 'RESIDENTIAL', type: 'VILLA' }
    },
    {
      title: 'Modern 3-Bedroom Freehold Villa for Sale in Prime Location of Ungasan Bali',
      description: '',
      expected: { category: 'RESIDENTIAL', type: 'VILLA' }
    },
    {
      title: 'Commercial Retail Space for Sale',
      description: 'Perfect for shop or business',
      expected: { category: 'COMMERCIAL', type: 'RETAIL' }
    },
    {
      title: 'Office Building for Sale',
      description: 'Modern office space',
      expected: { category: 'COMMERCIAL', type: 'OFFICE' }
    }
  ];
  
  testCases.forEach((testCase, i) => {
    console.log(`\n🧪 Test Case ${i + 1}:`);
    console.log(`Title: "${testCase.title}"`);
    console.log(`Description: "${testCase.description}"`);
    console.log(`Expected: ${testCase.expected.category} | ${testCase.expected.type}`);
    
    const result = classifyProperty(testCase.title, testCase.description);
    console.log(`Actual: ${result.category} | ${result.type}`);
    
    const isCorrect = result.category === testCase.expected.category && result.type === testCase.expected.type;
    console.log(`Result: ${isCorrect ? '✅ PASS' : '❌ FAIL'}`);
    
    if (!isCorrect) {
      console.log(`❌ MISMATCH: Expected ${testCase.expected.category}|${testCase.expected.type}, got ${result.category}|${result.type}`);
    }
  });
  
  console.log('\n🔍 Checking for potential issues...');
  
  // Check if there are any hidden characters or issues
  const problematicTitle = 'Brand-new 1 Bedroom Villa for Sale Leasehold in Bali Tumbak Bayuh';
  console.log(`\n📊 Character analysis of problematic title:`);
  console.log(`Length: ${problematicTitle.length}`);
  console.log(`Lowercase: "${problematicTitle.toLowerCase()}"`);
  console.log(`Contains 'villa': ${problematicTitle.toLowerCase().includes('villa')}`);
  console.log(`Contains 'retail': ${problematicTitle.toLowerCase().includes('retail')}`);
  console.log(`Contains 'commercial': ${problematicTitle.toLowerCase().includes('commercial')}`);
  console.log(`Contains 'shop': ${problematicTitle.toLowerCase().includes('shop')}`);
  console.log(`Contains 'office': ${problematicTitle.toLowerCase().includes('office')}`);
  console.log(`Contains 'store': ${problematicTitle.toLowerCase().includes('store')}`);
  console.log(`Contains 'business': ${problematicTitle.toLowerCase().includes('business')}`);
  
  // Check for any suspicious substrings
  const suspiciousWords = ['retail', 'commercial', 'shop', 'office', 'store', 'business'];
  suspiciousWords.forEach(word => {
    const index = problematicTitle.toLowerCase().indexOf(word);
    if (index !== -1) {
      console.log(`⚠️  Found '${word}' at position ${index}`);
      console.log(`   Context: "${problematicTitle.substring(Math.max(0, index-10), index+word.length+10)}"`);
    }
  });
}

debugClassification();
