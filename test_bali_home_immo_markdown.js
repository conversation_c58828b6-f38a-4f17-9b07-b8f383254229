require('dotenv').config();
const { parseBaliHomeImmoMarkdown } = require('./scrape_worker/mappers');

// The markdown content you provided
const testMarkdown = `Online

[WhatsApp](https://wa.me/6282194359401?text=Hi%20Bali%20Home%20Immo%20Team%2C%20%0AI%E2%80%99m%20reaching%20out%20through%20your%20website%20homepage%20regarding%20the%20following%20inquiry%3A "WhatsApp")[Messenger](https://m.me/BaliHomeImmo "Messenger")[Telegram](https://t.me/Balihomeimmo "Telegram")

# Luxury 2 Bedroom Penthouse for rent in Batu Belig - BHI1403C

68.500.000
/month

|     |     |     |
| --- | --- | --- |
| Bedroom | : | 2 |
| Bathroom | : | 2 |
| Land Size | : | 158 m² |

### General Information

|     |     |     |
| --- | --- | --- |
| Land Size | : | 158 m² |
| Building Size | : | 158 m² |
| Year of Build | : | 2023 |
| Floor Level | : | 1 |
| View | : | Garden |
| Style / Design | : | Modern |

### Indoor

|     |     |     |
| --- | --- | --- |
| Living room | : | Enclosed |
| Dinning room | : | Enclosed |
| Kitchen | : | Enclosed |
| Bedroom | : | 2 |
| Bathroom | : | 2 |
| Ensuite Bathroom | : | 2 |

Petitenget / Batu Belig
Batu Belig`;

async function testBaliHomeImmoMarkdown() {
  console.log('🧪 TESTING BALI HOME IMMO MARKDOWN PARSING');
  console.log('='.repeat(60));
  
  const url = 'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/petitenget-batu-belig/luxury-2-bedroom-penthouse-for-rent-in-batu-belig-bhi1403c';
  
  // Expected values
  const expected = {
    title: "Luxury 2 Bedroom Penthouse for rent in Batu Belig - BHI1403C",
    bedrooms: 2,
    bathrooms: 2,
    yearBuilt: 2023,
    location: "Batu Belig",
    landSize: 158,
    buildingSize: 158,
    price: "68.500.000",
    propertyType: "apartment"
  };
  
  console.log(`📋 Expected Values:`);
  console.log(`   Title: "${expected.title}"`);
  console.log(`   Bedrooms: ${expected.bedrooms}`);
  console.log(`   Bathrooms: ${expected.bathrooms}`);
  console.log(`   Year Built: ${expected.yearBuilt}`);
  console.log(`   Location: "${expected.location}"`);
  console.log(`   Land Size: ${expected.landSize} sqm`);
  console.log(`   Building Size: ${expected.buildingSize} sqm`);
  console.log(`   Price: "${expected.price}"`);
  console.log(`   Property Type: "${expected.propertyType}"`);
  
  try {
    console.log('\n🔍 PARSING MARKDOWN WITH FIXED PATTERNS...');
    
    // Test the parseBaliHomeImmoMarkdown function directly
    const data = parseBaliHomeImmoMarkdown(testMarkdown, url);
    
    if (data) {
      console.log(`\n✅ EXTRACTION RESULTS:`);
      console.log(`   Title: "${data.title}"`);
      console.log(`   Bedrooms: ${data.bedrooms}`);
      console.log(`   Bathrooms: ${data.bathrooms}`);
      console.log(`   Year Built: ${data.year_built}`);
      console.log(`   Location: "${data.location}"`);
      console.log(`   Land Size: ${data.size?.land_size_sqm} sqm`);
      console.log(`   Building Size: ${data.size?.building_size_sqm} sqm`);
      console.log(`   Price: ${data.price}`);
      console.log(`   Property Type: ${data.property_type}`);
      
      // Field-by-field comparison
      console.log(`\n📊 FIELD-BY-FIELD ANALYSIS:`);
      console.log('='.repeat(50));
      
      const checks = [
        { name: 'Title', expected: expected.title, actual: data.title },
        { name: 'Bedrooms', expected: expected.bedrooms, actual: data.bedrooms },
        { name: 'Bathrooms', expected: expected.bathrooms, actual: data.bathrooms },
        { name: 'Year Built', expected: expected.yearBuilt, actual: data.year_built },
        { name: 'Location', expected: expected.location, actual: data.location, contains: true },
        { name: 'Land Size', expected: expected.landSize, actual: data.size?.land_size_sqm },
        { name: 'Building Size', expected: expected.buildingSize, actual: data.size?.building_size_sqm },
        { name: 'Property Type', expected: expected.propertyType, actual: data.property_type }
      ];
      
      let correctCount = 0;
      let totalCount = checks.length;
      
      checks.forEach(check => {
        let isCorrect = false;
        
        if (check.contains) {
          // For location, check if expected is contained in actual
          isCorrect = check.actual && check.actual.toLowerCase().includes(check.expected.toLowerCase());
        } else {
          isCorrect = check.actual === check.expected;
        }
        
        if (isCorrect) correctCount++;
        
        console.log(`   ${isCorrect ? '✅' : '❌'} ${check.name}: Expected "${check.expected}", Got "${check.actual}"`);
      });
      
      // Price check (separate because it's a string)
      const priceMatch = data.price && data.price.includes(expected.price);
      if (priceMatch) correctCount++;
      totalCount++;
      console.log(`   ${priceMatch ? '✅' : '❌'} Price: Expected to contain "${expected.price}", Got "${data.price}"`);
      
      // Overall assessment
      console.log(`\n🎯 OVERALL RESULTS:`);
      console.log(`   ✅ Correct: ${correctCount}/${totalCount} fields`);
      console.log(`   📊 Success Rate: ${Math.round((correctCount/totalCount) * 100)}%`);
      
      if (correctCount === totalCount) {
        console.log(`   🎉 ALL FIXES WORKING PERFECTLY!`);
      } else {
        console.log(`   ⚠️ ${totalCount - correctCount} fields still need fixing`);
        
        // Show which fields are wrong
        console.log(`\n🔧 ISSUES FOUND:`);
        if (data.bathrooms !== expected.bathrooms) {
          console.log(`   - Bathrooms: Expected ${expected.bathrooms}, got ${data.bathrooms}`);
        }
        if (data.year_built !== expected.yearBuilt) {
          console.log(`   - Year Built: Expected ${expected.yearBuilt}, got ${data.year_built}`);
        }
        if (!data.location || !data.location.toLowerCase().includes(expected.location.toLowerCase())) {
          console.log(`   - Location: Expected to contain "${expected.location}", got "${data.location}"`);
        }
        if (data.size?.land_size_sqm !== expected.landSize) {
          console.log(`   - Land Size: Expected ${expected.landSize}, got ${data.size?.land_size_sqm}`);
        }
        if (data.size?.building_size_sqm !== expected.buildingSize) {
          console.log(`   - Building Size: Expected ${expected.buildingSize}, got ${data.size?.building_size_sqm}`);
        }
        if (!priceMatch) {
          console.log(`   - Price: Expected to contain "${expected.price}", got "${data.price}"`);
        }
        if (data.property_type !== expected.propertyType) {
          console.log(`   - Property Type: Expected "${expected.propertyType}", got "${data.property_type}"`);
        }
      }
      
    } else {
      console.log('❌ No data extracted - parsing returned null');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testBaliHomeImmoMarkdown();
