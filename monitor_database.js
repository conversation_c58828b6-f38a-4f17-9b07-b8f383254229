require('dotenv').config();
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { properties } = require('./db/schema');
const { desc, eq, and, gte, sql } = require('drizzle-orm');

// Database connection
const connectionString = process.env.DATABASE_URL;
const client = postgres(connectionString);
const db = drizzle(client);

async function monitorDatabase() {
  try {
    console.log('📊 DATABASE MONITORING - BetterPlace Properties\n');
    
    // Get total count of properties
    const totalCount = await db
      .select({ count: sql`count(*)` })
      .from(properties);
    
    console.log(`📈 Total Properties in Database: ${totalCount[0].count}`);
    
    // Get BetterPlace properties count
    const betterplaceCount = await db
      .select({ count: sql`count(*)` })
      .from(properties)
      .where(eq(properties.source_id, 'betterplace'));
    
    console.log(`🏠 BetterPlace Properties: ${betterplaceCount[0].count}`);
    
    // Get recent BetterPlace properties (last 10 minutes)
    const recentProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        external_id: properties.external_id,
        price: properties.price,
        location: properties.location,
        description: properties.description,
        created_at: properties.created_at,
        updated_at: properties.updated_at
      })
      .from(properties)
      .where(
        and(
          eq(properties.source_id, 'betterplace'),
          gte(properties.updated_at, sql`NOW() - INTERVAL '10 minutes'`)
        )
      )
      .orderBy(desc(properties.updated_at))
      .limit(10);
    
    if (recentProperties.length > 0) {
      console.log(`\n🆕 Recent BetterPlace Properties (last 10 minutes): ${recentProperties.length}`);
      console.log('=' .repeat(80));
      
      recentProperties.forEach((prop, i) => {
        console.log(`\n${i + 1}. ${prop.title}`);
        console.log(`   ID: ${prop.external_id}`);
        console.log(`   Price: ${prop.price ? `IDR ${prop.price.toLocaleString()}` : 'N/A'}`);
        console.log(`   Location: ${prop.location || 'N/A'}`);
        console.log(`   Updated: ${prop.updated_at}`);
        
        // Check description quality
        if (prop.description) {
          const descLength = prop.description.length;
          const hasGoodContent = prop.description.toLowerCase().includes('imagine stepping into') ||
                                prop.description.toLowerCase().includes('bali') ||
                                prop.description.toLowerCase().includes('property') ||
                                prop.description.toLowerCase().includes('villa') ||
                                prop.description.toLowerCase().includes('land');
          
          console.log(`   Description: ${descLength} chars ${hasGoodContent ? '✅' : '⚠️'}`);
          
          if (prop.description.toLowerCase().includes('imagine stepping into')) {
            console.log(`   🎯 CONTAINS BETTERPLACE DESCRIPTION!`);
          }
          
          // Show first 100 chars of description
          console.log(`   Preview: "${prop.description.substring(0, 100)}..."`);
        } else {
          console.log(`   Description: ❌ Missing`);
        }
      });
    } else {
      console.log('\n⏳ No recent BetterPlace properties found (last 10 minutes)');
    }
    
    // Get properties with good descriptions (containing "imagine stepping into")
    const goodDescriptions = await db
      .select({ count: sql`count(*)` })
      .from(properties)
      .where(
        and(
          eq(properties.source_id, 'betterplace'),
          sql`LOWER(description) LIKE '%imagine stepping into%'`
        )
      );
    
    console.log(`\n🎯 Properties with "Imagine stepping into" descriptions: ${goodDescriptions[0].count}`);
    
    // Get properties with short/generic descriptions
    const shortDescriptions = await db
      .select({ count: sql`count(*)` })
      .from(properties)
      .where(
        and(
          eq(properties.source_id, 'betterplace'),
          sql`LENGTH(description) < 200`
        )
      );
    
    console.log(`⚠️  Properties with short descriptions (<200 chars): ${shortDescriptions[0].count}`);
    
    // Show queue status
    console.log('\n📋 QUEUE STATUS:');
    try {
      const queueCount = await db
        .select({ count: sql`count(*)` })
        .from(sql`scraping_queue`);
      
      console.log(`   Remaining in queue: ${queueCount[0].count}`);
    } catch (error) {
      console.log('   Queue table not accessible or empty');
    }
    
    console.log('\n' + '='.repeat(80));
    console.log(`📊 Monitoring completed at ${new Date().toLocaleTimeString()}`);
    
  } catch (error) {
    console.error('❌ Database monitoring failed:', error.message);
  } finally {
    await client.end();
  }
}

// Run monitoring
monitorDatabase();
