require('dotenv').config();
const { Pool } = require('pg');

async function checkSpecificProperties() {
  console.log('🔍 CHECKING SPECIFIC PROPERTIES');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get the specific properties
    const result = await pool.query(`
      SELECT 
        id,
        external_id,
        title,
        bedrooms,
        bathrooms,
        category,
        type,
        year_built,
        created_at,
        last_scraped_at,
        source_url
      FROM property 
      WHERE id IN ('48dca317-e72f-426b-8ee5-37aa747b2885', '0acfa66b-4c22-41c0-a023-6e50ad7bf84d')
      ORDER BY created_at DESC
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ Properties not found in database');
      return;
    }
    
    console.log(`📊 Found ${result.rows.length} properties`);
    console.log('\n📋 PROPERTY DETAILS:');
    console.log('='.repeat(120));
    
    result.rows.forEach((prop, index) => {
      console.log(`\n${index + 1}. PROPERTY: ${prop.id}`);
      console.log(`   External ID: ${prop.external_id}`);
      console.log(`   Title: ${prop.title}`);
      console.log(`   URL: ${prop.source_url}`);
      console.log(`   Created: ${prop.created_at}`);
      console.log(`   Last Scraped: ${prop.last_scraped_at}`);
      console.log('');
      
      // Analyze bedroom extraction
      const titleBedroomMatch = prop.title.match(/(\d+)\s*bedroom/i);
      const expectedBedrooms = titleBedroomMatch ? parseInt(titleBedroomMatch[1]) : null;
      
      console.log(`   📊 BEDROOM ANALYSIS:`);
      console.log(`      Title contains: "${titleBedroomMatch ? titleBedroomMatch[0] : 'No bedroom mention'}"`);
      console.log(`      Expected bedrooms: ${expectedBedrooms}`);
      console.log(`      Database bedrooms: ${prop.bedrooms} ${prop.bedrooms === expectedBedrooms ? '✅' : '❌'}`);
      
      // Analyze bathroom extraction
      console.log(`   📊 BATHROOM ANALYSIS:`);
      console.log(`      Database bathrooms: ${prop.bathrooms} ${prop.bathrooms ? '✅' : '❌'}`);
      
      // Analyze property type
      const external_id = prop.external_id || '';
      let expectedCategory = 'RESIDENTIAL';
      let expectedType = 'VILLA';
      
      if (external_id.startsWith('BPLL') || external_id.startsWith('BPLD') || external_id.startsWith('BPLF')) {
        expectedCategory = 'LAND';
        expectedType = 'LAND';
      } else if (prop.title.toLowerCase().includes('apartment')) {
        expectedType = 'APARTMENT';
      } else if (prop.title.toLowerCase().includes('villa')) {
        expectedType = 'VILLA';
      }
      
      console.log(`   📊 CLASSIFICATION ANALYSIS:`);
      console.log(`      Expected: ${expectedCategory} ${expectedType}`);
      console.log(`      Database: ${prop.category} ${prop.type} ${(prop.category === expectedCategory && prop.type === expectedType) ? '✅' : '❌'}`);
      
      // Analyze year built
      console.log(`   📊 YEAR BUILT ANALYSIS:`);
      console.log(`      Database year: ${prop.year_built} ${prop.year_built ? '✅' : '❌'}`);
      
      // Overall status
      const bedroomOK = !expectedBedrooms || prop.bedrooms === expectedBedrooms;
      const bathroomOK = prop.bathrooms !== null;
      const classificationOK = prop.category === expectedCategory && prop.type === expectedType;
      const yearOK = prop.year_built !== null;
      
      const overallStatus = bedroomOK && bathroomOK && classificationOK && yearOK;
      
      console.log(`\n   🎯 OVERALL STATUS: ${overallStatus ? '✅ ALL GOOD' : '❌ HAS ISSUES'}`);
      if (!overallStatus) {
        console.log(`      Issues:`);
        if (!bedroomOK) console.log(`        - Bedroom extraction: Expected ${expectedBedrooms}, got ${prop.bedrooms}`);
        if (!bathroomOK) console.log(`        - Bathroom extraction: No bathroom count found`);
        if (!classificationOK) console.log(`        - Classification: Expected ${expectedCategory} ${expectedType}, got ${prop.category} ${prop.type}`);
        if (!yearOK) console.log(`        - Year built: No year found`);
      }
    });
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkSpecificProperties();
