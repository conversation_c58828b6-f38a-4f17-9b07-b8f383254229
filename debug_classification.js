require('dotenv').config();

// Copy the classification function to test it
function classifyProperty(title, description) {
  const text = `${title} ${description}`.toLowerCase();
  
  console.log(`🔍 Testing classification for: "${text}"`);

  // Commercial property detection - use more precise patterns to avoid false positives
  const commercialPatterns = [
    /\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent)\b/,
    /\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/,
    /\b(commercial\s+property|commercial\s+building|commercial\s+space)\b/,
    /\b(store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/,
    /\b(warehouse\s+for\s+sale|warehouse\s+for\s+rent|storage\s+facility)\b/,
    /\b(business\s+premises|business\s+property)\b/
  ];

  // Check if any commercial pattern matches
  console.log('🔍 Testing commercial patterns:');
  commercialPatterns.forEach((pattern, index) => {
    const matches = pattern.test(text);
    console.log(`   Pattern ${index + 1}: ${pattern} → ${matches ? '✅ MATCH' : '❌ No match'}`);
  });
  
  const isCommercial = commercialPatterns.some(pattern => pattern.test(text));
  console.log(`📊 Is Commercial: ${isCommercial}`);

  if (isCommercial) {
    console.log('🏢 Classified as COMMERCIAL');
    if (/\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'OFFICE' };
    }
    if (/\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent|store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'RETAIL' };
    }
    if (/\b(warehouse\s+for\s+sale|warehouse\s+for\s+rent|storage\s+facility)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'WAREHOUSE' };
    }
    return { category: 'COMMERCIAL', type: 'OTHER' };
  }

  console.log('🔍 Testing residential patterns:');
  
  // Residential property detection
  if (text.includes('villa')) {
    console.log('🏠 Found "villa" → RESIDENTIAL VILLA');
    return { category: 'RESIDENTIAL', type: 'VILLA' };
  }
  if (text.includes('apartment') || text.includes('apt')) {
    console.log('🏠 Found "apartment" or "apt" → RESIDENTIAL APARTMENT');
    return { category: 'RESIDENTIAL', type: 'APARTMENT' };
  }
  if (text.includes('house') || text.includes('home')) {
    console.log('🏠 Found "house" or "home" → RESIDENTIAL HOUSE');
    return { category: 'RESIDENTIAL', type: 'HOUSE' };
  }
  if (text.includes('condo') || text.includes('condominium')) {
    console.log('🏠 Found "condo" → RESIDENTIAL CONDO');
    return { category: 'RESIDENTIAL', type: 'CONDO' };
  }
  if (text.includes('townhouse') || text.includes('town house')) {
    console.log('🏠 Found "townhouse" → RESIDENTIAL TOWNHOUSE');
    return { category: 'RESIDENTIAL', type: 'TOWNHOUSE' };
  }
  if (text.includes('loft')) {
    console.log('🏠 Found "loft" → RESIDENTIAL LOFT');
    return { category: 'RESIDENTIAL', type: 'LOFT' };
  }

  // Land detection
  if (text.includes('land') || text.includes('plot') || text.includes('lot') || text.includes('terrain')) {
    console.log('🌍 Found land keywords → LAND');
    return { category: 'LAND', type: 'LAND' };
  }

  // Industrial detection
  if (text.includes('factory') || text.includes('manufacturing') || text.includes('industrial')) {
    console.log('🏭 Found industrial keywords → INDUSTRIAL');
    return { category: 'INDUSTRIAL', type: 'OTHER' };
  }

  console.log('🏠 No specific match, defaulting to RESIDENTIAL VILLA');
  // Default to residential villa for Bali properties
  return { category: 'RESIDENTIAL', type: 'VILLA' };
}

// Test the problematic title
const title = "Modern 2 Bedroom Suite Apartments in Tumbak Bayuh";
const description = "";

console.log('🧪 TESTING CLASSIFICATION');
console.log('='.repeat(60));

const result = classifyProperty(title, description);

console.log('\n📊 FINAL RESULT:');
console.log(`   Category: ${result.category}`);
console.log(`   Type: ${result.type}`);

if (result.category === 'COMMERCIAL') {
  console.log('\n❌ PROBLEM: This should be RESIDENTIAL, not COMMERCIAL!');
} else {
  console.log('\n✅ Correct classification');
}
