require('dotenv').config();

// Copy the fixed function to test it
function detectBetterPlacePropertyType(property_id, title, markdown) {
  const id = property_id?.toUpperCase() || '';
  const text = `${title} ${markdown}`.toLowerCase();

  console.log(`🔍 Testing property type detection:`);
  console.log(`   ID: ${id}`);
  console.log(`   Text: "${text.substring(0, 100)}..."`);

  // Content based detection FIRST (more accurate for edge cases)
  if (text.includes('apartment') || text.includes('condo')) {
    console.log(`   ✅ Content-based: Found "apartment" → apartment`);
    return 'apartment';
  }
  if (text.includes('land') || text.includes('plot') || text.includes('lot')) {
    console.log(`   ✅ Content-based: Found land keywords → land`);
    return 'land';
  }
  if (text.includes('hotel') || text.includes('resort')) {
    console.log(`   ✅ Content-based: Found hotel keywords → hotel`);
    return 'hotel';
  }
  if (text.includes('villa') || text.includes('house')) {
    console.log(`   ✅ Content-based: Found villa keywords → villa`);
    return 'villa';
  }

  console.log(`   🔍 No content match, checking ID patterns...`);

  // Property ID based detection as fallback (when content is unclear)
  if (id.startsWith('BPVL')) {
    console.log(`   📋 ID-based: BPVL → villa`);
    return 'villa';
  }
  if (id.startsWith('BPVF')) {
    console.log(`   📋 ID-based: BPVF → villa`);
    return 'villa';
  }
  if (id.startsWith('BPHL')) {
    console.log(`   📋 ID-based: BPHL → hotel`);
    return 'hotel';
  }
  if (id.startsWith('BPAP')) {
    console.log(`   📋 ID-based: BPAP → apartment`);
    return 'apartment';
  }

  console.log(`   🏠 Default fallback → villa`);
  return 'villa';
}

// Test the problematic case
console.log('🧪 TESTING APARTMENT CLASSIFICATION FIX');
console.log('='.repeat(60));

const result = detectBetterPlacePropertyType(
  'BPHL00890',
  'Modern 2 Bedroom Suite Apartments in Tumbak Bayuh',
  ''
);

console.log(`\n📊 RESULT: ${result}`);

if (result === 'apartment') {
  console.log('✅ SUCCESS! Now correctly detects as apartment');
} else {
  console.log('❌ FAILED! Still not detecting as apartment');
}
