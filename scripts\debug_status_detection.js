// Debug status detection issue
function detectPropertyStatus(title, description) {
  const text = (title + ' ' + description).toLowerCase();
  console.log(`   🔍 Combined text: "${text}"`);

  // Check for sold indicators
  if (text.includes('(sold)') || text.includes('sold out') || text.includes('no longer available')) {
    console.log('   ✅ Detected SOLD');
    return 'SOLD';
  }

  // Check for rented indicators
  if (text.includes('(rented)') || text.includes('rented out') || text.includes('no longer for rent')) {
    console.log('   ✅ Detected RENTED');
    return 'RENTED';
  }

  // Check for pending indicators
  if (text.includes('(pending)') || text.includes('under offer') || text.includes('reserved')) {
    console.log('   ✅ Detected PENDING');
    return 'PENDING';
  }

  // Check for unavailable indicators (be specific to avoid false positives)
  if (text.includes('not available') || text.includes('no longer available') || 
      text.includes('unavailable') || text.includes('off market') ||
      text.includes('withdrawn from market') || text.includes('removed from market')) {
    console.log('   ⚠️  Detected INACTIVE');
    return 'INACTIVE';
  }

  // Default to available
  console.log('   ✅ Defaulting to AVAILABLE');
  return 'AVAILABLE';
}

console.log('🔍 Debugging Status Detection\n');

// Test the problematic case
const title = '3 Bedroom Villa for Sale Leasehold in Bali Tabanan';
const description = 'Beautiful villa available for sale in Tabanan.';

console.log('Testing problematic case:');
console.log(`Title: "${title}"`);
console.log(`Description: "${description}"`);

const result = detectPropertyStatus(title, description);
console.log(`Final result: ${result}`);

console.log('\n---\n');

// Test SOLD case
const soldTitle = 'Kerobokan 4 Bedroom Private Pool Villa (SOLD)';
const soldDescription = 'This beautiful villa has been sold and is no longer available.';

console.log('Testing SOLD case:');
console.log(`Title: "${soldTitle}"`);
console.log(`Description: "${soldDescription}"`);

const soldResult = detectPropertyStatus(soldTitle, soldDescription);
console.log(`Final result: ${soldResult}`);
