require('dotenv').config();
const { Pool } = require('pg');

async function checkSpecificPropertyAgain() {
  console.log('🔍 CHECKING SPECIFIC PROPERTY: 0acfa66b-4c22-41c0-a023-6e50ad7bf84d');
  console.log('='.repeat(80));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get the specific property
    const result = await pool.query(`
      SELECT 
        id,
        external_id,
        title,
        bedrooms,
        bathrooms,
        category,
        type,
        year_built,
        created_at,
        last_scraped_at,
        source_url
      FROM property 
      WHERE id = '0acfa66b-4c22-41c0-a023-6e50ad7bf84d'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ Property not found in database');
      return;
    }
    
    const prop = result.rows[0];
    
    console.log(`📊 CURRENT DATABASE VALUES:`);
    console.log(`   ID: ${prop.id}`);
    console.log(`   External ID: ${prop.external_id}`);
    console.log(`   Title: ${prop.title}`);
    console.log(`   URL: ${prop.source_url}`);
    console.log(`   Last Scraped: ${prop.last_scraped_at}`);
    console.log('');
    
    // Detailed analysis
    console.log(`📋 DETAILED ANALYSIS:`);
    console.log(`   Title: "${prop.title}"`);
    
    // Test all bedroom patterns manually
    const bedroomPatterns = [
      { name: 'bedroom', pattern: /(\d+)\s*[-\s]*bedroom/i },
      { name: 'bed', pattern: /(\d+)\s*[-\s]*bed(?:room)?/i },
      { name: 'BR', pattern: /(\d+)\s*br\b/i },
      { name: 'BDR', pattern: /(\d+)\s*bdr\b/i }
    ];
    
    console.log(`\n🔍 TESTING BEDROOM PATTERNS:`);
    bedroomPatterns.forEach(({ name, pattern }) => {
      const match = prop.title.match(pattern);
      if (match) {
        console.log(`   ✅ ${name} pattern: "${match[0]}" → ${match[1]} bedrooms`);
      } else {
        console.log(`   ❌ ${name} pattern: No match`);
      }
    });
    
    // Expected vs actual
    const expectedBedrooms = 3; // From title "3BR"
    console.log(`\n📊 COMPARISON:`);
    console.log(`   Expected bedrooms: ${expectedBedrooms}`);
    console.log(`   Database bedrooms: ${prop.bedrooms} ${prop.bedrooms === expectedBedrooms ? '✅' : '❌'}`);
    console.log(`   Expected bathrooms: 2-4 (reasonable range)`);
    console.log(`   Database bathrooms: ${prop.bathrooms} ${prop.bathrooms >= 2 && prop.bathrooms <= 4 ? '✅' : '❌'}`);
    
    // Check if recently scraped
    const scrapedRecently = prop.last_scraped_at && new Date(prop.last_scraped_at) > new Date(Date.now() - 60 * 60 * 1000); // Last hour
    console.log(`\n⏰ TIMING:`);
    console.log(`   Scraped in last hour: ${scrapedRecently ? '✅ Yes' : '❌ No'}`);
    
    if (!scrapedRecently) {
      console.log(`\n🎯 ISSUE: Property was not scraped recently with the new fixes`);
      console.log(`   The property needs to be re-scraped to get the correct values`);
      console.log(`   Current values are from the old extraction logic`);
    } else {
      console.log(`\n❌ PROBLEM: Property was scraped recently but still has wrong values`);
      console.log(`   This indicates the fixes are not working as expected`);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkSpecificPropertyAgain();
