require('dotenv').config();

async function fixExtractionPatterns() {
  console.log('🔧 FIXING EXTRACTION PATTERNS');
  
  // Test HTML from the real website
  const testHtml = `
    ![bedrooms](/_next/static/media/bedrooms.7a6788f7.svg)3
    ![bathrooms](/_next/static/media/bathrooms.45d31171.svg)4
  `;
  
  console.log('🔍 Testing current patterns:');
  
  // Current broken patterns
  const currentBedroomPattern = /bedrooms\.7a6788f7\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
  const currentBathroomPattern = /bathrooms\.45d31171\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
  
  console.log(`Current bedroom: ${testHtml.match(currentBedroomPattern) ? 'FOUND' : 'NOT FOUND'}`);
  console.log(`Current bathroom: ${testHtml.match(currentBathroomPattern) ? 'FOUND' : 'NOT FOUND'}`);
  
  // NEW CORRECT PATTERNS based on actual HTML structure
  const newBedroomPattern = /bedrooms\.7a6788f7\.svg.*?(\d+)/;
  const newBathroomPattern = /bathrooms\.45d31171\.svg.*?(\d+)/;
  
  const bedroomMatch = testHtml.match(newBedroomPattern);
  const bathroomMatch = testHtml.match(newBathroomPattern);
  
  console.log(`\n✅ NEW PATTERNS:`)
  console.log(`Bedroom: ${bedroomMatch ? bedroomMatch[1] : 'NOT FOUND'}`);
  console.log(`Bathroom: ${bathroomMatch ? bathroomMatch[1] : 'NOT FOUND'}`);
  
  if (bedroomMatch && bathroomMatch) {
    console.log('\n🎯 NEW PATTERNS WORK! Updating mapper...');
    return {
      bedroomPattern: newBedroomPattern,
      bathroomPattern: newBathroomPattern
    };
  } else {
    console.log('\n❌ Need to find better patterns');
    return null;
  }
}

fixExtractionPatterns();
