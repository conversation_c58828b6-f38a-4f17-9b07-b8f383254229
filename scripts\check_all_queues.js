// Check queue status for all 4 websites
require('dotenv').config();
const { db, scrapingQueue } = require('../drizzle_client');
const { eq, sql } = require('drizzle-orm');

async function checkAllQueues() {
  try {
    console.log('🔍 Checking queue status for all 4 websites:\n');
    
    const websites = [
      { id: 'villabalisale.com', name: 'Villa Bali Sale' },
      { id: 'betterplace.co.id', name: 'BetterPlace' },
      { id: 'balihomeimmo.com', name: 'Bali Home Immo' },
      { id: 'balivillarealty.com', name: 'Bali Villa Realty' }
    ];
    
    let totalUrls = 0;
    const availableWebsites = [];
    
    for (const website of websites) {
      const count = await db
        .select({ count: sql`count(*)` })
        .from(scrapingQueue)
        .where(eq(scrapingQueue.website_id, website.id));
      
      const urlCount = parseInt(count[0].count);
      totalUrls += urlCount;
      
      console.log(`📊 ${website.name} (${website.id}): ${urlCount} URLs in queue`);
      
      if (urlCount > 0) {
        availableWebsites.push(website);
        
        const sample = await db
          .select()
          .from(scrapingQueue)
          .where(eq(scrapingQueue.website_id, website.id))
          .limit(3);
        
        console.log('   Sample URLs:');
        sample.forEach((item, i) => {
          console.log(`   ${i + 1}. ${item.url.substring(0, 70)}...`);
        });
      } else {
        console.log('   ❌ No URLs available for testing');
      }
      console.log('');
    }
    
    console.log('📈 Summary:');
    console.log(`   Total URLs across all sites: ${totalUrls}`);
    console.log(`   Websites with URLs: ${availableWebsites.length}/4`);
    
    if (availableWebsites.length === 0) {
      console.log('\n🚨 No URLs available for testing!');
      console.log('💡 You need to run the listing page scrapers first:');
      console.log('   - node scrape_worker/listing_scraper.js betterplace.co.id');
      console.log('   - node scrape_worker/listing_scraper.js balihomeimmo.com');
      console.log('   - node scrape_worker/listing_scraper.js balivillarealty.com');
    } else if (availableWebsites.length < 4) {
      console.log('\n⚠️  Only some websites have URLs available for testing.');
      console.log('Available for testing:');
      availableWebsites.forEach(site => {
        console.log(`   ✅ ${site.name}`);
      });
      
      const missingWebsites = websites.filter(w => !availableWebsites.find(a => a.id === w.id));
      console.log('\nMissing URLs for:');
      missingWebsites.forEach(site => {
        console.log(`   ❌ ${site.name} - Run listing scraper first`);
      });
    } else {
      console.log('\n🎉 All 4 websites have URLs available for testing!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

checkAllQueues();
