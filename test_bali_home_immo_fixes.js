require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testBaliHomeImmoFixes() {
  console.log('🧪 TESTING BALI HOME IMMO FIXES');
  console.log('='.repeat(60));
  
  const url = 'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/petitenget-batu-belig/luxury-2-bedroom-penthouse-for-rent-in-batu-belig-bhi1403c';
  
  // Expected values from the markdown
  const expected = {
    title: "Luxury 2 Bedroom Penthouse for rent in Batu Belig - BHI1403C",
    bedrooms: 2,
    bathrooms: 2,
    yearBuilt: 2023,
    location: "Batu Belig",
    landSize: 158,
    buildingSize: 158,
    price: 68500000,
    category: "RESIDENTIAL",
    type: "APARTMENT"
  };
  
  console.log(`🔍 URL: ${url}`);
  console.log(`📋 Expected Values:`);
  console.log(`   Title: "${expected.title}"`);
  console.log(`   Bedrooms: ${expected.bedrooms}`);
  console.log(`   Bathrooms: ${expected.bathrooms}`);
  console.log(`   Year Built: ${expected.yearBuilt}`);
  console.log(`   Location: "${expected.location}"`);
  console.log(`   Land Size: ${expected.landSize} sqm`);
  console.log(`   Building Size: ${expected.buildingSize} sqm`);
  console.log(`   Price: ${expected.price} IDR`);
  console.log(`   Classification: ${expected.category} ${expected.type}`);
  
  try {
    console.log('\n🕷️ SCRAPING WITH FIXED EXTRACTION LOGIC...');
    
    const result = await runExtractBatch('bali_home_immo', [url], {});
    
    if (result && result.extractedData && result.extractedData.length > 0) {
      const data = result.extractedData[0];
      
      console.log(`\n✅ EXTRACTION RESULTS:`);
      console.log(`   Title: "${data.title}"`);
      console.log(`   Bedrooms: ${data.bedrooms}`);
      console.log(`   Bathrooms: ${data.bathrooms}`);
      console.log(`   Year Built: ${data.year_built}`);
      console.log(`   Location: "${data.location}"`);
      console.log(`   Land Size: ${data.size?.land_size_sqm} sqm`);
      console.log(`   Building Size: ${data.size?.building_size_sqm} sqm`);
      console.log(`   Price: ${data.price}`);
      console.log(`   Property Type: ${data.property_type}`);
      
      // Field-by-field comparison
      console.log(`\n📊 FIELD-BY-FIELD ANALYSIS:`);
      console.log('='.repeat(50));
      
      const checks = [
        { name: 'Title', expected: expected.title, actual: data.title },
        { name: 'Bedrooms', expected: expected.bedrooms, actual: data.bedrooms },
        { name: 'Bathrooms', expected: expected.bathrooms, actual: data.bathrooms },
        { name: 'Year Built', expected: expected.yearBuilt, actual: data.year_built },
        { name: 'Location', expected: expected.location, actual: data.location, contains: true },
        { name: 'Land Size', expected: expected.landSize, actual: data.size?.land_size_sqm },
        { name: 'Building Size', expected: expected.buildingSize, actual: data.size?.building_size_sqm },
        { name: 'Property Type', expected: expected.type.toLowerCase(), actual: data.property_type }
      ];
      
      let correctCount = 0;
      let totalCount = checks.length;
      
      checks.forEach(check => {
        let isCorrect = false;
        
        if (check.contains) {
          // For location, check if expected is contained in actual
          isCorrect = check.actual && check.actual.toLowerCase().includes(check.expected.toLowerCase());
        } else {
          isCorrect = check.actual === check.expected;
        }
        
        if (isCorrect) correctCount++;
        
        console.log(`   ${isCorrect ? '✅' : '❌'} ${check.name}: Expected "${check.expected}", Got "${check.actual}"`);
      });
      
      // Price check (separate because it's a string)
      const priceMatch = data.price && data.price.includes('68.500.000');
      if (priceMatch) correctCount++;
      totalCount++;
      console.log(`   ${priceMatch ? '✅' : '❌'} Price: Expected "68.500.000", Got "${data.price}"`);
      
      // Overall assessment
      console.log(`\n🎯 OVERALL RESULTS:`);
      console.log(`   ✅ Correct: ${correctCount}/${totalCount} fields`);
      console.log(`   📊 Success Rate: ${Math.round((correctCount/totalCount) * 100)}%`);
      
      if (correctCount === totalCount) {
        console.log(`   🎉 ALL FIXES WORKING PERFECTLY!`);
      } else {
        console.log(`   ⚠️ ${totalCount - correctCount} fields still need fixing`);
      }
      
      // Specific issue analysis
      if (correctCount < totalCount) {
        console.log(`\n🔧 REMAINING ISSUES:`);
        if (data.bathrooms !== expected.bathrooms) {
          console.log(`   - Bathroom extraction: Got ${data.bathrooms}, expected ${expected.bathrooms}`);
        }
        if (data.year_built !== expected.yearBuilt) {
          console.log(`   - Year built extraction: Got ${data.year_built}, expected ${expected.yearBuilt}`);
        }
        if (!data.location || !data.location.toLowerCase().includes('batu belig')) {
          console.log(`   - Location extraction: Got "${data.location}", expected to contain "Batu Belig"`);
        }
        if (data.size?.land_size_sqm !== expected.landSize) {
          console.log(`   - Land size extraction: Got ${data.size?.land_size_sqm}, expected ${expected.landSize}`);
        }
        if (data.size?.building_size_sqm !== expected.buildingSize) {
          console.log(`   - Building size extraction: Got ${data.size?.building_size_sqm}, expected ${expected.buildingSize}`);
        }
        if (!priceMatch) {
          console.log(`   - Price extraction: Got "${data.price}", expected to contain "68.500.000"`);
        }
        if (data.property_type !== expected.type.toLowerCase()) {
          console.log(`   - Property type: Got "${data.property_type}", expected "${expected.type.toLowerCase()}"`);
        }
      }
      
    } else {
      console.log('❌ No data extracted');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testBaliHomeImmoFixes();
