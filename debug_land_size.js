// Debug land size pattern matching
const testMarkdown = `| Land Size | : | 158 m² |

### General Information

| Land Size | : | 158 m² |
| Building Size | : | 158 m² |`;

console.log('🔍 DEBUGGING LAND SIZE PATTERN MATCHING');
console.log('='.repeat(50));

// Test land size patterns from the Bali Home Immo mapper
const landSizePatterns = [
  { name: 'Explicit land patterns', pattern: /(?:land|lot|plot|site)\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i },
  { name: 'Reverse patterns', pattern: /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m).*(?:land|lot|plot|site)/i },
  { name: 'Simple patterns', pattern: /(?:land|lot|plot|site)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i },
  { name: 'List patterns', pattern: /[-•]\s*(?:land|lot|plot|site)\s*(?:size)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i },
  { name: 'Table patterns (FIXED)', pattern: /(?:land|lot|plot|site)(?:\s*size)?\s*[|:\s]+(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i }
];

console.log('Testing patterns against markdown:');
console.log('Markdown content:');
console.log(testMarkdown);
console.log('\nPattern results:');

landSizePatterns.forEach(({ name, pattern }) => {
  const match = testMarkdown.match(pattern);
  if (match) {
    console.log(`✅ ${name}: Found "${match[0]}" → ${match[1]}`);
    
    // Show context around the match
    const index = testMarkdown.indexOf(match[0]);
    const context = testMarkdown.substring(Math.max(0, index - 20), index + match[0].length + 20);
    console.log(`   Context: "...${context}..."`);
  } else {
    console.log(`❌ ${name}: No match`);
  }
});

// Test the exact logic from the Bali Home Immo mapper
console.log('\n🔍 TESTING EXACT MAPPER LOGIC:');

const landSizeMatch = testMarkdown.match(/(?:land|lot|plot|site)\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i) ||
                     testMarkdown.match(/(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m).*(?:land|lot|plot|site)/i) ||
                     testMarkdown.match(/(?:land|lot|plot|site)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i) ||
                     testMarkdown.match(/[-•]\s*(?:land|lot|plot|site)\s*(?:size)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i) ||
                     testMarkdown.match(/(?:land|lot|plot|site)(?:\s*size)?\s*[|:\s]+(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i);

console.log('Land size match result:', landSizeMatch);
if (landSizeMatch) {
  console.log('Full match:', landSizeMatch[0]);
  console.log('Captured number:', landSizeMatch[1]);
  console.log('Parsed float:', parseFloat(landSizeMatch[1].replace(',', '')));
} else {
  console.log('❌ No land size match found');
}
