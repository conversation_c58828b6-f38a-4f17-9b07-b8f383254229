require('dotenv').config();
const { Pool } = require('pg');

async function thoroughInvestigation() {
  console.log('🔍 THOROUGH INVESTIGATION: BPVL00910');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get complete database record
    const result = await pool.query(`
      SELECT *
      FROM property 
      WHERE id = '800abea8-ad82-4cf4-8c06-d88dcb5f4be5'
         OR external_id = 'BPVL00910'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ Property not found in database');
      return;
    }
    
    const property = result.rows[0];
    
    console.log(`📊 COMPLETE DATABASE RECORD:`);
    console.log(`   ID: ${property.id}`);
    console.log(`   External ID: ${property.external_id}`);
    console.log(`   Title: ${property.title}`);
    console.log(`   URL: ${property.source_url}`);
    console.log(`   Bedrooms: ${property.bedrooms}`);
    console.log(`   Bathrooms: ${property.bathrooms}`);
    console.log(`   Building Size: ${property.size_sqft} sqft`);
    console.log(`   Land Size: ${property.lot_size_sqft} sqft`);
    console.log(`   Price: ${property.price} IDR`);
    console.log(`   Year Built: ${property.year_built}`);
    console.log(`   Address: ${property.address}`);
    console.log(`   City: ${property.city}`);
    console.log(`   Created: ${property.created_at}`);
    console.log(`   Last Scraped: ${property.last_scraped_at}`);
    
    console.log('\n🌐 URL TO INVESTIGATE:');
    console.log(`   ${property.source_url}`);
    
    console.log('\n🎯 INVESTIGATION PLAN:');
    console.log('1. ✅ Database record retrieved');
    console.log('2. 🔄 Next: Scrape fresh data from website');
    console.log('3. 🔄 Next: Compare with manual browser check');
    console.log('4. 🔄 Next: Analyze extraction patterns');
    
    return property;
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

thoroughInvestigation();
