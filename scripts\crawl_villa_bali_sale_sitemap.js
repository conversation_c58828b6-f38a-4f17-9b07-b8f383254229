// Crawl Villa Bali Sale new sitemap and discover property URLs
require('dotenv').config();
const { db, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { eq, sql } = require('drizzle-orm');

async function crawlVillaBaliSaleSitemap() {
  try {
    console.log('🗺️  Crawling Villa Bali Sale New Sitemap\n');
    
    const sitemapUrl = 'https://www.villabalisale.com/sitemap_property.xml';
    console.log(`📡 Fetching sitemap: ${sitemapUrl}`);
    
    // Fetch sitemap content
    const response = await fetch(sitemapUrl);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const sitemapContent = await response.text();
    console.log(`✅ Sitemap fetched successfully (${sitemapContent.length} chars)`);
    
    // Extract URLs from sitemap XML
    const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
    if (!urlMatches) {
      console.log('❌ No URLs found in sitemap');
      return;
    }
    
    const urls = urlMatches.map(match => match.replace(/<\/?loc>/g, ''));
    console.log(`🔍 Found ${urls.length} URLs in sitemap`);
    
    // Show first 10 URLs as examples
    console.log('\n📋 Sample URLs from sitemap:');
    urls.slice(0, 10).forEach((url, i) => {
      console.log(`   ${i + 1}. ${url}`);
    });
    
    if (urls.length > 10) {
      console.log(`   ... and ${urls.length - 10} more URLs`);
    }
    
    // Analyze URL patterns
    console.log('\n🔍 Analyzing URL patterns:');
    
    const patterns = {
      saleProperties: urls.filter(url => url.includes('/for-sale/villa/')),
      rentProperties: urls.filter(url => url.includes('/for-rent/villa/')),
      uniqueVillas: urls.filter(url => url.includes('/unique-villas/')),
      listingPages: urls.filter(url => url.includes('/realestate-property/') && !url.includes('/for-sale/villa/') && !url.includes('/for-rent/villa/')),
      otherPages: urls.filter(url => !url.includes('/realestate-property/') && !url.includes('/unique-villas/'))
    };
    
    console.log(`   🏠 Sale properties: ${patterns.saleProperties.length}`);
    console.log(`   🏡 Rent properties: ${patterns.rentProperties.length}`);
    console.log(`   ⭐ Unique villas: ${patterns.uniqueVillas.length}`);
    console.log(`   📋 Listing pages: ${patterns.listingPages.length}`);
    console.log(`   📄 Other pages: ${patterns.otherPages.length}`);
    
    // Show examples of each type
    if (patterns.saleProperties.length > 0) {
      console.log('\n🏠 Sale property examples:');
      patterns.saleProperties.slice(0, 3).forEach((url, i) => {
        console.log(`   ${i + 1}. ${url}`);
      });
    }
    
    if (patterns.rentProperties.length > 0) {
      console.log('\n🏡 Rent property examples:');
      patterns.rentProperties.slice(0, 3).forEach((url, i) => {
        console.log(`   ${i + 1}. ${url}`);
      });
    }
    
    // Test our URL patterns against these URLs
    console.log('\n🧪 Testing URL patterns against sitemap URLs:');
    
    const propertyPatterns = [
      /\/realestate-property\/for-sale\/villa\/(freehold|leasehold)\/[^\/]+\/[^\/]+-[a-z0-9]+\/?$/,
      /\/realestate-property\/for-rent\/villa\/(annually|monthly|daily|weekly)\/[^\/]+\/[^\/]+-[a-z0-9]+\/?$/,
      /\/realestate-property\/for-sale\/villa\/(freehold|leasehold)\/[^\/]+\/[^\/]+\/?$/,
      /\/realestate-property\/for-rent\/villa\/(annually|monthly|daily|weekly)\/[^\/]+\/[^\/]+\/?$/,
      /\/unique-villas\/[^\/]+\/?$/
    ];
    
    let matchedUrls = 0;
    const matchedUrlsList = [];
    
    urls.forEach(url => {
      const isMatch = propertyPatterns.some(pattern => pattern.test(url));
      if (isMatch) {
        matchedUrls++;
        matchedUrlsList.push(url);
      }
    });
    
    console.log(`   ✅ URLs matching our patterns: ${matchedUrls}/${urls.length}`);
    console.log(`   📊 Match rate: ${((matchedUrls / urls.length) * 100).toFixed(1)}%`);
    
    if (matchedUrlsList.length > 0) {
      console.log('\n📋 First 5 matching URLs:');
      matchedUrlsList.slice(0, 5).forEach((url, i) => {
        console.log(`   ${i + 1}. ${url}`);
      });
    }
    
    console.log('\n🎯 Summary:');
    console.log(`   📊 Total URLs in sitemap: ${urls.length}`);
    console.log(`   🏠 Property URLs (sale): ${patterns.saleProperties.length}`);
    console.log(`   🏡 Property URLs (rent): ${patterns.rentProperties.length}`);
    console.log(`   ⭐ Unique villas: ${patterns.uniqueVillas.length}`);
    console.log(`   ✅ URLs matching patterns: ${matchedUrls}`);
    console.log(`   🎯 Ready for URL discovery: ${matchedUrls > 0 ? 'YES' : 'NO'}`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    closeConnection();
  }
}

crawlVillaBaliSaleSitemap();
