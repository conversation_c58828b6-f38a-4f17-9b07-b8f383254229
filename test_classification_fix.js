require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testClassificationFix() {
  console.log('🧪 TESTING CLASSIFICATION FIX');
  console.log('='.repeat(60));
  
  // Test different property types
  const testUrls = [
    'https://betterplace.cc/buy/properties/BPLL00727', // Land
    'https://betterplace.cc/buy/properties/BPVF00745', // Villa
    'https://betterplace.cc/buy/properties/BPHL00746'  // Apartment
  ];
  
  const expected = [
    { id: 'BPLL00727', type: 'LAND', category: 'LAND' },
    { id: 'BPVF00745', type: 'VILLA', category: 'RESIDENTIAL' },
    { id: 'BPHL00746', type: 'APARTMENT', category: 'RESIDENTIAL' }
  ];
  
  for (let i = 0; i < testUrls.length; i++) {
    const url = testUrls[i];
    const exp = expected[i];
    
    console.log(`\n${i + 1}. TESTING: ${exp.id}`);
    console.log(`   URL: ${url}`);
    console.log(`   Expected: ${exp.category} ${exp.type}`);
    
    try {
      const result = await runExtractBatch('betterplace', [url], {});
      
      if (result && result.extractedData && result.extractedData.length > 0) {
        const data = result.extractedData[0];
        
        console.log(`   ✅ RESULT:`);
        console.log(`      Category: ${data.category} ${data.category === exp.category ? '✅' : '❌'}`);
        console.log(`      Type: ${data.type} ${data.type === exp.type ? '✅' : '❌'}`);
        console.log(`      Property Type: ${data.property_type}`);
        console.log(`      Title: ${data.title.substring(0, 50)}...`);
        
        if (data.category === exp.category && data.type === exp.type) {
          console.log(`   🎉 SUCCESS!`);
        } else {
          console.log(`   ❌ FAILED!`);
        }
        
      } else {
        console.log(`   ❌ No data extracted`);
      }
      
    } catch (error) {
      console.error(`   ❌ Error: ${error.message}`);
    }
  }
}

testClassificationFix();
