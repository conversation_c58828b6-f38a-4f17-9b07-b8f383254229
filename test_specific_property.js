require('dotenv').config();
const { Pool } = require('pg');
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testSpecificProperty() {
  console.log('🔍 TESTING PROPERTY: 800abea8-ad82-4cf4-8c06-d88dcb5f4be5');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get the specific property from database
    const result = await pool.query(`
      SELECT 
        id,
        external_id,
        title,
        bedrooms,
        bathrooms,
        source_url,
        created_at
      FROM property 
      WHERE id = '800abea8-ad82-4cf4-8c06-d88dcb5f4be5'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ Property not found in database');
      return;
    }
    
    const property = result.rows[0];
    
    console.log(`🏠 DATABASE VALUES:`);
    console.log(`   ID: ${property.id}`);
    console.log(`   External ID: ${property.external_id}`);
    console.log(`   URL: ${property.source_url}`);
    console.log(`   Database: ${property.bedrooms} bed / ${property.bathrooms} bath`);
    console.log(`   Title: ${property.title}`);
    console.log(`   Created: ${property.created_at}`);
    
    console.log('\n🔄 RE-SCRAPING WITH CURRENT MAPPER...');
    
    try {
      // Use the actual scraper to get fresh data
      const scrapingResult = await runExtractBatch('betterplace', [property.source_url], {});
      
      if (scrapingResult && scrapingResult.extractedData && scrapingResult.extractedData.length > 0) {
        const freshData = scrapingResult.extractedData[0];
        
        console.log(`\n🆕 FRESH SCRAPE RESULTS:`);
        console.log(`   Bedrooms: ${freshData.bedrooms}`);
        console.log(`   Bathrooms: ${freshData.bathrooms}`);
        console.log(`   Title: ${freshData.title}`);
        console.log(`   Price: ${freshData.price}`);
        
        // Compare
        const bedroomMatch = freshData.bedrooms === property.bedrooms;
        const bathroomMatch = freshData.bathrooms === property.bathrooms;
        
        console.log(`\n📊 DETAILED COMPARISON:`);
        console.log(`   Bedrooms: ${property.bedrooms} (DB) vs ${freshData.bedrooms} (Fresh) ${bedroomMatch ? '✅' : '❌'}`);
        console.log(`   Bathrooms: ${property.bathrooms} (DB) vs ${freshData.bathrooms} (Fresh) ${bathroomMatch ? '✅' : '❌'}`);
        
        if (!bedroomMatch || !bathroomMatch) {
          console.log(`\n❌ MISMATCH DETECTED!`);
          console.log(`💾 DATABASE: ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms`);
          console.log(`🆕 FRESH: ${freshData.bedrooms} bedrooms, ${freshData.bathrooms} bathrooms`);
          console.log(`\n🎯 ACTION NEEDED: Manual verification required`);
          console.log(`   1. Check ${property.source_url} manually`);
          console.log(`   2. Verify actual bedroom/bathroom count`);
          console.log(`   3. Determine if database or mapper is wrong`);
        } else {
          console.log(`\n✅ Perfect match! Values are consistent.`);
        }
        
      } else {
        console.log('❌ Fresh scraping failed - no data returned');
        if (scrapingResult) {
          console.log('Scraping result:', scrapingResult);
        }
      }
      
    } catch (error) {
      console.error(`❌ Error scraping: ${error.message}`);
      console.error('Stack trace:', error.stack);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

testSpecificProperty();
