// Apply real URL patterns based on complete sitemap analysis
require('dotenv').config();
const postgres = require('postgres');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

async function applyRealUrlPatterns() {
  console.log('🎯 Applying Real URL Patterns Based on Complete Sitemap Analysis\n');
  
  try {
    // Bali Home Immo - Based on actual sitemap analysis
    const baliHomePatterns = {
      property_patterns: [
        // Individual property pages with full path structure
        "/realestate-property/for-sale/villa/leasehold/[^/]+/[^/]+$",
        "/realestate-property/for-sale/villa/freehold/[^/]+/[^/]+$", 
        "/realestate-property/for-rent/villa/yearly/[^/]+/[^/]+$",
        "/realestate-property/for-rent/villa/monthly/[^/]+/[^/]+$",
        "/realestate-property/for-sale/apartment/[^/]+/[^/]+/[^/]+$",
        "/realestate-property/for-rent/apartment/[^/]+/[^/]+/[^/]+$"
      ],
      listing_patterns: [
        // Category and listing pages
        "/realestate-property/$",
        "/realestate-property/for-sale/$",
        "/realestate-property/for-rent/$",
        "/realestate-property/for-sale/villa/$",
        "/realestate-property/for-rent/villa/$",
        "/realestate-property/for-sale/apartment/$",
        "/realestate-property/for-rent/apartment/$",
        "/realestate-property/for-sale/villa/leasehold/$",
        "/realestate-property/for-sale/villa/freehold/$",
        "/realestate-property/for-rent/villa/yearly/$",
        "/realestate-property/for-rent/villa/monthly/$",
        "/featured-property/",
        "/search",
        "/category/"
      ],
      exclude_patterns: [
        "/blog/",           // Blog articles (241 URLs)
        "/testimonial/",    // Testimonials
        "/faq$",
        "/contact$", 
        "/about$",
        "/$"               // Homepage
      ],
      keywords: ["bedroom", "villa", "apartment", "sale", "rent", "leasehold", "freehold", "bali"]
    };
    
    await client`
      UPDATE website_configs 
      SET property_url_patterns = ${JSON.stringify(baliHomePatterns)}
      WHERE website_id = 'bali_home_immo'
    `;
    console.log('✅ Updated Bali Home Immo patterns (based on 9,405 URLs analysis)');
    
    // Bali Villa Realty - Based on actual sitemap analysis  
    const baliVillaPatterns = {
      property_patterns: [
        // Almost all URLs are individual properties: /property/[name]/
        "/property/[^/]+/$"
      ],
      listing_patterns: [
        // Only the main property page is a listing
        "/property/$",
        "/properties/$",
        "/search",
        "/category/"
      ],
      exclude_patterns: [
        "/contact$",
        "/about$", 
        "/services$",
        "/$"
      ],
      keywords: ["bedroom", "villa", "sale", "rent", "leasehold", "freehold", "bali"]
    };
    
    await client`
      UPDATE website_configs 
      SET property_url_patterns = ${JSON.stringify(baliVillaPatterns)}
      WHERE website_id = 'bali_villa_realty'
    `;
    console.log('✅ Updated Bali Villa Realty patterns (based on 1,134 URLs analysis)');
    
    // BetterPlace - Based on actual sitemap analysis
    const betterplacePatterns = {
      property_patterns: [
        // Individual property pages with specific ID patterns
        "/buy/properties/BPVL\\d+$",    // Villa leasehold (1,218 URLs)
        "/buy/properties/BPVF\\d+$",    // Villa freehold  
        "/buy/properties/BPHL\\d+$",    // Hotel/Land
        "/rent/properties/\\d+$"        // Rental properties (483 URLs)
      ],
      listing_patterns: [
        // Category and search pages
        "/buy/properties$",
        "/rent/properties$", 
        "/buy/properties\\?",
        "/rent/properties\\?",
        "/buy/indonesia/",
        "/rent/indonesia/"
      ],
      exclude_patterns: [
        "/blog/",
        "/about$",
        "/contact$",
        "/help$",
        "/$"
      ],
      keywords: ["bedroom", "bathroom", "villa", "apartment", "price", "sqm"]
    };
    
    await client`
      UPDATE website_configs 
      SET property_url_patterns = ${JSON.stringify(betterplacePatterns)}
      WHERE website_id = 'betterplace'
    `;
    console.log('✅ Updated BetterPlace patterns (based on 3,769 URLs analysis)');
    
    // Test the real patterns
    console.log('\n🧪 Testing Real Patterns with Actual URLs...');
    await testRealPatterns();
    
    // Show expected results
    console.log('\n📊 Expected Classification Results:');
    console.log('🌐 Bali Home Immo: ~8,000+ property pages, ~200 listing pages, ~200 other');
    console.log('🌐 Bali Villa Realty: ~1,125 property pages, ~9 listing pages');  
    console.log('🌐 BetterPlace: ~1,700 property pages, ~2,000 listing pages');
    
    console.log('\n🎉 Real URL patterns applied successfully!');
    
  } catch (error) {
    console.error('❌ Pattern application failed:', error.message);
    console.error(error.stack);
  } finally {
    await client.end();
  }
}

async function testRealPatterns() {
  const { SitemapParser } = require('../scrape_worker/sitemap_parser');
  const parser = new SitemapParser();
  
  // Test with actual URLs from the analysis
  const testUrls = {
    bali_home_immo: [
      'https://bali-home-immo.com/realestate-property/for-sale/villa/leasehold/canggu/luxury-villa-canggu-rf123',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/yearly/canggu/yearly-rental-villa',
      'https://bali-home-immo.com/realestate-property/for-sale/',
      'https://bali-home-immo.com/blog/property-investment-tips',
      'https://bali-home-immo.com/testimonial/great-service-123'
    ],
    bali_villa_realty: [
      'https://balivillarealty.com/property/brand-new-3-bedroom-villa-for-yearly-rental-in-bali-kerobokan/',
      'https://balivillarealty.com/property/moa-villa-canggu-by-ilot-property-bali/',
      'https://balivillarealty.com/property/',
      'https://balivillarealty.com/about'
    ],
    betterplace: [
      'https://betterplace.cc/buy/properties/BPVL02257',
      'https://betterplace.cc/buy/properties/BPVF02256', 
      'https://betterplace.cc/rent/properties/500017040',
      'https://betterplace.cc/buy/properties',
      'https://betterplace.cc/blog/property-tips'
    ]
  };
  
  for (const [websiteId, urls] of Object.entries(testUrls)) {
    console.log(`\n🌐 Testing ${websiteId}:`);
    
    // Get the updated config
    const configs = await client`
      SELECT property_url_patterns 
      FROM website_configs 
      WHERE website_id = ${websiteId}
    `;
    
    const config = {
      website_id: websiteId,
      property_url_patterns: JSON.parse(configs[0].property_url_patterns)
    };
    
    urls.forEach(url => {
      const classification = parser.classifyUrl(url, config);
      const type = classification.isProperty ? '🏠 Property' : 
                  classification.isListing ? '📋 Listing' : '📄 Other';
      console.log(`  ${type}: ${url}`);
      console.log(`    Confidence: ${classification.confidence}, Reason: ${classification.reason}`);
    });
  }
}

applyRealUrlPatterns();
