// Debug bathroom pattern matching
const testMarkdown = `# Luxury 2 Bedroom Penthouse for rent in Batu Belig - BHI1403C

68.500.000
/month

|     |     |     |
| --- | --- | --- |
| Bedroom | : | 2 |
| Bathroom | : | 2 |
| Land Size | : | 158 m² |

### Indoor

|     |     |     |
| --- | --- | --- |
| Bedroom | : | 2 |
| Bathroom | : | 2 |
| Ensuite Bathroom | : | 2 |`;

console.log('🔍 DEBUGGING BATHROOM PATTERN MATCHING');
console.log('='.repeat(50));

// Test all bathroom patterns from the Bali Home Immo mapper
const bathroomPatterns = [
  { name: 'Standard patterns', pattern: /(\d{1,2})\s*(?:bath|bathroom|kamar mandi)s?(?!\d)/i },
  { name: 'Colon/space patterns', pattern: /(?:bath|bathroom)s?[:\s]+(\d{1,2})(?!\d)/i },
  { name: 'Abbreviation patterns', pattern: /(\d{1,2})\s*BA(?!\d)/i },
  { name: 'Property details patterns', pattern: /[-•]\s*(\d{1,2})\s*(?:bath|bathroom)s?/i },
  { name: 'Slash patterns', pattern: /\d+\s*(?:bed|bedroom|BR)s?[\/\s]*(\d{1,2})\s*(?:bath|bathroom|BA)s?/i },
  { name: 'Indonesian patterns', pattern: /(\d{1,2})\s*kamar\s*mandi/i },
  { name: 'Table/list patterns (FIXED)', pattern: /(?:bath|bathroom)s?\s*[|:\s]+(\d{1,2})/i }
];

console.log('Testing patterns against markdown:');
console.log('Markdown content:');
console.log(testMarkdown);
console.log('\nPattern results:');

bathroomPatterns.forEach(({ name, pattern }) => {
  const match = testMarkdown.match(pattern);
  if (match) {
    console.log(`✅ ${name}: Found "${match[0]}" → ${match[1]}`);
    
    // Show context around the match
    const index = testMarkdown.indexOf(match[0]);
    const context = testMarkdown.substring(Math.max(0, index - 20), index + match[0].length + 20);
    console.log(`   Context: "...${context}..."`);
  } else {
    console.log(`❌ ${name}: No match`);
  }
});

// Test the exact logic from the Bali Home Immo mapper
console.log('\n🔍 TESTING EXACT MAPPER LOGIC:');

const bathroomMatch =
  // Standard patterns: "2 bathrooms", "3 bath", "2 kamar mandi"
  testMarkdown.match(/(\d{1,2})\s*(?:bath|bathroom|kamar mandi)s?(?!\d)/i) ||
  // Colon/space patterns: "Bathrooms: 2", "Bath: 3"
  testMarkdown.match(/(?:bath|bathroom)s?[:\s]+(\d{1,2})(?!\d)/i) ||
  // Abbreviation patterns: "2 BA", "3BA"
  testMarkdown.match(/(\d{1,2})\s*BA(?!\d)/i) ||
  // Property details patterns: "- 2 bathrooms", "• 3 bath"
  testMarkdown.match(/[-•]\s*(\d{1,2})\s*(?:bath|bathroom)s?/i) ||
  // Slash patterns: "2/3" (bed/bath), "3BR/2BA"
  testMarkdown.match(/\d+\s*(?:bed|bedroom|BR)s?[\/\s]*(\d{1,2})\s*(?:bath|bathroom|BA)s?/i) ||
  // Indonesian patterns: "2 kamar mandi"
  testMarkdown.match(/(\d{1,2})\s*kamar\s*mandi/i) ||
  // Table/list patterns: "Bathrooms | 2", "Bath | 3", "| Bathroom | : | 2 |"
  testMarkdown.match(/(?:bath|bathroom)s?\s*[|:\s]+(\d{1,2})/i);

console.log('Bathroom match result:', bathroomMatch);
if (bathroomMatch) {
  console.log('Full match:', bathroomMatch[0]);
  console.log('Captured number:', bathroomMatch[1]);
  console.log('Parsed integer:', parseInt(bathroomMatch[1]));

  const bathrooms = parseInt(bathroomMatch[1]);
  console.log('Final bathroom count:', bathrooms);

  // Test validation
  if (bathrooms && (bathrooms > 15 || bathrooms < 1)) {
    console.log(`⚠️ Suspicious bathroom count: ${bathrooms}, would be set to null`);
  } else {
    console.log(`✅ Valid bathroom count: ${bathrooms}`);
  }
} else {
  console.log('❌ No bathroom match found');
}
