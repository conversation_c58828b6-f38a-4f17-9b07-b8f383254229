require('dotenv').config();
const { Pool } = require('pg');

async function clearPropertyTable() {
  console.log('🗑️ CLEARING PROPERTY TABLE');
  console.log('='.repeat(50));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // First, get count of current properties
    const countResult = await pool.query('SELECT COUNT(*) as count FROM property');
    const currentCount = parseInt(countResult.rows[0].count);
    
    console.log(`📊 Current properties in table: ${currentCount}`);
    
    if (currentCount === 0) {
      console.log('✅ Table is already empty');
      return;
    }
    
    // Clear the property table
    console.log('🗑️ Deleting all properties...');
    const deleteResult = await pool.query('DELETE FROM property');
    
    console.log(`✅ Deleted ${deleteResult.rowCount} properties`);
    
    // Verify table is empty
    const verifyResult = await pool.query('SELECT COUNT(*) as count FROM property');
    const finalCount = parseInt(verifyResult.rows[0].count);
    
    console.log(`📊 Properties remaining: ${finalCount}`);
    
    if (finalCount === 0) {
      console.log('🎉 SUCCESS! Property table is now empty');
      console.log('\n🚀 Ready to start fresh scraping with all fixes:');
      console.log('   ✅ Bedroom/bathroom extraction from Details section');
      console.log('   ✅ Year built extraction with realistic validation');
      console.log('   ✅ Apartment classification as RESIDENTIAL');
      console.log('   ✅ Loft classification as RESIDENTIAL');
      console.log('   ✅ Title-based bedroom extraction priority');
    } else {
      console.log('❌ ERROR: Table not fully cleared');
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

clearPropertyTable();
