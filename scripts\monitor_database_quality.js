// Monitor database quality during scraping
require('dotenv').config();
const { db, properties, closeConnection } = require('../drizzle_client');
const { sql, desc, eq } = require('drizzle-orm');

async function monitorDatabaseQuality() {
  try {
    console.log('📊 Database Quality Monitoring\n');
    
    // Get total count
    const totalCount = await db
      .select({ count: sql`count(*)` })
      .from(properties);
    
    console.log(`📈 Total Properties: ${totalCount[0].count}`);
    
    // Get counts by source
    const sourceCounts = await db
      .select({
        source: properties.source,
        count: sql`count(*)`.as('count')
      })
      .from(properties)
      .groupBy(properties.source)
      .orderBy(sql`count(*) DESC`);

    console.log('\n🌐 Properties by Source:');
    if (sourceCounts && sourceCounts.length > 0) {
      sourceCounts.forEach(row => {
        console.log(`   ${row.source}: ${row.count}`);
      });
    } else {
      console.log('   No properties found');
    }
    
    // Get latest 10 properties with detailed field analysis
    const latestProperties = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log('\n🔍 Latest 10 Properties - Field Quality Analysis:');
    console.log('='.repeat(80));
    
    let fieldStats = {
      title: 0,
      price: 0,
      rent_price: 0,
      location: 0,
      bedrooms: 0,
      bathrooms: 0,
      size_sqft: 0,
      lot_size_sqft: 0,
      ownership_type: 0,
      year_built: 0,
      description: 0,
      images: 0
    };
    
    if (latestProperties && latestProperties.length > 0) {
      latestProperties.forEach((prop, i) => {
      console.log(`\n${i + 1}. ${prop.title}`);
      console.log(`   🌐 Source: ${prop.source}`);
      console.log(`   📅 Created: ${prop.created_at.toLocaleString()}`);
      
      // Price analysis
      if (prop.price) {
        console.log(`   💰 Sale Price: IDR ${prop.price.toLocaleString()}`);
        fieldStats.price++;
      } else if (prop.rent_price) {
        console.log(`   🏡 Rent Price: IDR ${prop.rent_price.toLocaleString()}/month`);
        fieldStats.rent_price++;
      } else {
        console.log(`   ⚠️  No Price: Missing both sale and rent price`);
      }
      
      // Location analysis
      if (prop.city && prop.state) {
        console.log(`   📍 Location: ${prop.city}, ${prop.state}, ${prop.country}`);
        fieldStats.location++;
      } else {
        console.log(`   ⚠️  Location: ${prop.city || 'No city'}, ${prop.state || 'No state'}`);
      }
      
      // Property details
      if (prop.bedrooms) {
        console.log(`   🛏️  Bedrooms: ${prop.bedrooms}`);
        fieldStats.bedrooms++;
      }
      
      if (prop.bathrooms) {
        console.log(`   🚿 Bathrooms: ${prop.bathrooms}`);
        fieldStats.bathrooms++;
      }
      
      // Size information
      if (prop.size_sqft) {
        console.log(`   📐 Building Size: ${prop.size_sqft} sqft`);
        fieldStats.size_sqft++;
      }
      
      if (prop.lot_size_sqft) {
        console.log(`   🏞️  Land Size: ${prop.lot_size_sqft} sqft`);
        fieldStats.lot_size_sqft++;
      }
      
      // Ownership
      if (prop.ownership_type) {
        console.log(`   🏛️  Ownership: ${prop.ownership_type}`);
        fieldStats.ownership_type++;
      }
      
      // Year built
      if (prop.year_built) {
        console.log(`   📅 Year Built: ${prop.year_built}`);
        fieldStats.year_built++;
      }
      
      // Description quality
      if (prop.description && prop.description.length > 50) {
        console.log(`   📝 Description: ${prop.description.length} chars (Good)`);
        fieldStats.description++;
      } else {
        console.log(`   ⚠️  Description: ${prop.description?.length || 0} chars (Poor)`);
      }
      
      // Images
      const imageCount = prop.media?.image_count || 0;
      if (imageCount > 0) {
        console.log(`   🖼️  Images: ${imageCount} images`);
        fieldStats.images++;
      } else {
        console.log(`   ⚠️  Images: No images`);
      }
      
      // Count fields with data
      fieldStats.title++;
    });
    } else {
      console.log('\n⚠️  No properties found in database yet');
    }
    
    // Field completion statistics
    if (latestProperties && latestProperties.length > 0) {
      console.log('\n📊 Field Completion Statistics (Last 10 Properties):');
      console.log('='.repeat(60));

      const total = latestProperties.length;
      Object.entries(fieldStats).forEach(([field, count]) => {
        const percentage = ((count / total) * 100).toFixed(1);
        const status = percentage >= 80 ? '✅' : percentage >= 50 ? '⚠️' : '❌';
        console.log(`   ${status} ${field}: ${count}/${total} (${percentage}%)`);
      });

      // Overall quality assessment
      console.log('\n🎯 Overall Quality Assessment:');
      const avgCompletion = Object.values(fieldStats).reduce((a, b) => a + b, 0) / Object.keys(fieldStats).length / total * 100;
      console.log(`   📈 Average Field Completion: ${avgCompletion.toFixed(1)}%`);

      if (avgCompletion >= 70) {
        console.log('   🎉 EXCELLENT: Data quality is very good!');
      } else if (avgCompletion >= 50) {
        console.log('   ✅ GOOD: Data quality is acceptable');
      } else {
        console.log('   ⚠️  NEEDS IMPROVEMENT: Data quality could be better');
      }
    }
    
    // Check for recent additions
    const recentCount = await db
      .select({ count: sql`count(*)` })
      .from(properties)
      .where(sql`created_at > NOW() - INTERVAL '10 minutes'`);
    
    console.log(`\n⏱️  Properties added in last 10 minutes: ${recentCount[0].count}`);
    
    if (recentCount[0].count > 0) {
      console.log('✅ Scraping is actively adding new properties!');
    } else {
      console.log('⚠️  No recent additions - scraping may have stopped');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    closeConnection();
  }
}

monitorDatabaseQuality();
