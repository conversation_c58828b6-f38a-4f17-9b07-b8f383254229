// Check Bali Coconut Living status in database
require('dotenv').config();
const { db, websiteConfigs, discoveredUrls, scrapingQueue, properties } = require('../drizzle_client');
const { eq, sql } = require('drizzle-orm');

async function checkBaliCoconutStatus() {
  console.log('🥥 Checking Bali Coconut Living Status');
  console.log('='.repeat(50));
  
  try {
    // 1. Check website configuration
    console.log('\n1️⃣ Website Configuration:');
    const config = await db
      .select()
      .from(websiteConfigs)
      .where(eq(websiteConfigs.website_id, 'bali_coconut_living'))
      .limit(1);
    
    if (config.length > 0) {
      const cfg = config[0];
      console.log(`   ✅ Configuration found: ${cfg.name}`);
      console.log(`   🌐 Base URL: ${cfg.base_url}`);
      console.log(`   📊 Active: ${cfg.is_active}`);
      console.log(`   🗺️  Sitemap enabled: ${cfg.sitemap_enabled}`);
      console.log(`   📅 Last crawl: ${cfg.last_crawl_at || 'Never'}`);
      console.log(`   🔄 Crawl frequency: ${cfg.crawl_frequency_hours} hours`);
    } else {
      console.log('   ❌ No configuration found for bali_coconut_living');
    }
    
    // 2. Check discovered URLs
    console.log('\n2️⃣ Discovered URLs:');
    const discoveredCount = await db
      .select({ count: sql`count(*)` })
      .from(discoveredUrls)
      .where(eq(discoveredUrls.website_id, 'bali_coconut_living'));
    
    console.log(`   📊 Total discovered URLs: ${discoveredCount[0]?.count || 0}`);
    
    if (discoveredCount[0]?.count > 0) {
      const sampleUrls = await db
        .select({
          url: discoveredUrls.url,
          url_type: discoveredUrls.url_type,
          is_property_page: discoveredUrls.is_property_page,
          confidence_score: discoveredUrls.confidence_score
        })
        .from(discoveredUrls)
        .where(eq(discoveredUrls.website_id, 'bali_coconut_living'))
        .limit(5);
      
      console.log('   📋 Sample URLs:');
      sampleUrls.forEach((url, i) => {
        console.log(`      ${i + 1}. ${url.url_type}: ${url.url} (confidence: ${url.confidence_score})`);
      });
    }
    
    // 3. Check scraping queue
    console.log('\n3️⃣ Scraping Queue:');
    const queueStats = await db
      .select({
        status: scrapingQueue.status,
        count: sql`count(*)`
      })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'bali_coconut_living'))
      .groupBy(scrapingQueue.status);
    
    if (queueStats.length > 0) {
      console.log('   📊 Queue status breakdown:');
      queueStats.forEach(stat => {
        console.log(`      ${stat.status}: ${stat.count}`);
      });
    } else {
      console.log('   ❌ No URLs in scraping queue');
    }
    
    // 4. Check properties in database
    console.log('\n4️⃣ Properties in Database:');
    const propertyCount = await db
      .select({ count: sql`count(*)` })
      .from(properties)
      .where(eq(properties.source_id, 'bali_coconut_living'));
    
    console.log(`   📊 Total properties: ${propertyCount[0]?.count || 0}`);
    
    if (propertyCount[0]?.count > 0) {
      const sampleProperties = await db
        .select({
          title: properties.title,
          price: properties.price,
          rent_price: properties.rent_price,
          city: properties.city,
          ownership_type: properties.ownership_type
        })
        .from(properties)
        .where(eq(properties.source_id, 'bali_coconut_living'))
        .limit(5);
      
      console.log('   📋 Sample properties:');
      sampleProperties.forEach((prop, i) => {
        const priceInfo = prop.price ? `Sale: ${prop.price}` : 
                         prop.rent_price ? `Rent: ${prop.rent_price}` : 'No price';
        console.log(`      ${i + 1}. ${prop.title} (${prop.city}) - ${priceInfo}`);
      });
    }
    
    // 5. Summary and recommendations
    console.log('\n📋 Summary:');
    const hasConfig = config.length > 0;
    const hasUrls = discoveredCount[0]?.count > 0;
    const hasQueue = queueStats.length > 0;
    const hasProperties = propertyCount[0]?.count > 0;
    
    console.log(`   Configuration: ${hasConfig ? '✅' : '❌'}`);
    console.log(`   Discovered URLs: ${hasUrls ? '✅' : '❌'}`);
    console.log(`   Scraping Queue: ${hasQueue ? '✅' : '❌'}`);
    console.log(`   Properties: ${hasProperties ? '✅' : '❌'}`);
    
    if (!hasConfig) {
      console.log('\n🔧 Recommendation: Run setup script to add Bali Coconut Living configuration');
    } else if (!hasUrls) {
      console.log('\n🔧 Recommendation: Run sitemap crawling to discover URLs');
    } else if (!hasQueue) {
      console.log('\n🔧 Recommendation: Add discovered URLs to scraping queue');
    } else if (!hasProperties) {
      console.log('\n🔧 Recommendation: Process scraping queue to extract properties');
    } else {
      console.log('\n✅ Bali Coconut Living is fully integrated and operational!');
    }
    
  } catch (error) {
    console.error('❌ Error checking Bali Coconut Living status:', error.message);
  }
}

checkBaliCoconutStatus().then(() => process.exit(0));
