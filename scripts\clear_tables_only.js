// Clear Tables Only - Simple script to empty property and scraping_queue tables
require('dotenv').config();
const { db, properties, scrapingQueue, discoveredUrls, crawlJobs, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function clearTablesOnly() {
  console.log('🗑️  Clearing Database Tables (keeping website_configs)\n');
  
  try {
    // 1. Clear property table
    console.log('🏠 Clearing property table...');
    await db.delete(properties);
    console.log('   ✅ Cleared property table');
    
    // 2. Clear scraping_queue table
    console.log('📋 Clearing scraping_queue table...');
    await db.delete(scrapingQueue);
    console.log('   ✅ Cleared scraping_queue table');
    
    // 3. Clear discovered_urls table
    console.log('🔗 Clearing discovered_urls table...');
    await db.delete(discoveredUrls);
    console.log('   ✅ Cleared discovered_urls table');
    
    // 4. Clear crawl_jobs table
    console.log('🕷️  Clearing crawl_jobs table...');
    await db.delete(crawlJobs);
    console.log('   ✅ Cleared crawl_jobs table');
    
    // 5. Verify tables are empty
    console.log('\n🔍 Verifying table states...');
    
    const propertyCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`   📊 Properties: ${propertyCount[0]?.count || 0} records`);

    const queueCount = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue`);
    console.log(`   📋 Scraping Queue: ${queueCount[0]?.count || 0} records`);

    const urlsCount = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls`);
    console.log(`   🔗 Discovered URLs: ${urlsCount[0]?.count || 0} records`);

    const jobsCount = await db.execute(sql`SELECT COUNT(*) as count FROM crawl_jobs`);
    console.log(`   🕷️  Crawl Jobs: ${jobsCount[0]?.count || 0} records`);
    
    console.log('\n🎉 Tables cleared successfully!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Run test scripts to populate database');
    console.log('   2. Example: node scripts/test_all_4_websites_ownership.js');
    
  } catch (error) {
    console.error('❌ Error clearing tables:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the clear operation
clearTablesOnly()
  .then(() => {
    console.log('✅ Tables cleared successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Clear operation failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
