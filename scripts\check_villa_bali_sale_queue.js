// Check Villa Bali Sale URLs in queue
require('dotenv').config();
const { db, scrapingQueue, discoveredUrls, closeConnection } = require('../drizzle_client');
const { eq, sql } = require('drizzle-orm');

async function checkVillaBaliSaleQueue() {
  try {
    console.log('🏠 Checking Villa Bali Sale URL Status\n');
    
    // Check scraping queue
    console.log('📊 Scraping Queue Status:');
    const queueCounts = await db
      .select({
        website_id: scrapingQueue.website_id,
        count: sql`count(*)`.as('count')
      })
      .from(scrapingQueue)
      .groupBy(scrapingQueue.website_id);
    
    queueCounts.forEach(row => {
      console.log(`   ${row.website_id}: ${row.count} URLs`);
    });
    
    // Check specifically for Villa Bali Sale
    const villaBaliSaleQueue = await db
      .select()
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villabalisale.com'))
      .limit(5);
    
    console.log(`\n🔍 Villa Bali Sale Queue (villabalisale.com): ${villaBaliSaleQueue.length} URLs`);
    if (villaBaliSaleQueue.length > 0) {
      villaBaliSaleQueue.forEach((url, i) => {
        console.log(`   ${i + 1}. ${url.url}`);
      });
    }
    
    // Also check with different website_id formats
    const altFormats = ['villa_bali_sale', 'villabalisale', 'Villa Bali Sale'];
    
    for (const format of altFormats) {
      const altQueue = await db
        .select()
        .from(scrapingQueue)
        .where(eq(scrapingQueue.website_id, format))
        .limit(5);
      
      if (altQueue.length > 0) {
        console.log(`\n🔍 Found URLs with website_id "${format}": ${altQueue.length} URLs`);
        altQueue.forEach((url, i) => {
          console.log(`   ${i + 1}. ${url.url}`);
        });
      }
    }
    
    // Check discovered URLs
    console.log('\n📊 Discovered URLs Status:');
    const discoveredCounts = await db
      .select({
        website_id: discoveredUrls.website_id,
        count: sql`count(*)`.as('count')
      })
      .from(discoveredUrls)
      .groupBy(discoveredUrls.website_id);
    
    discoveredCounts.forEach(row => {
      console.log(`   ${row.website_id}: ${row.count} URLs`);
    });
    
    // Check Villa Bali Sale discovered URLs
    const villaBaliSaleDiscovered = await db
      .select()
      .from(discoveredUrls)
      .where(eq(discoveredUrls.website_id, 'villabalisale.com'))
      .limit(10);
    
    console.log(`\n🔍 Villa Bali Sale Discovered URLs: ${villaBaliSaleDiscovered.length} URLs`);
    if (villaBaliSaleDiscovered.length > 0) {
      villaBaliSaleDiscovered.forEach((url, i) => {
        console.log(`   ${i + 1}. ${url.url} (${url.url_type})`);
      });
    }
    
    // Check if URLs contain villabalisale.com
    const villaBaliSaleUrls = await db
      .select()
      .from(scrapingQueue)
      .where(sql`url LIKE '%villabalisale.com%'`)
      .limit(10);
    
    console.log(`\n🔍 URLs containing 'villabalisale.com': ${villaBaliSaleUrls.length} URLs`);
    if (villaBaliSaleUrls.length > 0) {
      villaBaliSaleUrls.forEach((url, i) => {
        console.log(`   ${i + 1}. ${url.url} (website_id: ${url.website_id})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    closeConnection();
  }
}

checkVillaBaliSaleQueue();
