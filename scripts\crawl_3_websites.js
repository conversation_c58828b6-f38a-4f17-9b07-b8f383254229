// Crawl 3 specific websites: BetterPlace, Bali Home Immo, Villa Bali Sale
require('dotenv').config();
const { SmartCrawler } = require('../scrape_worker/smart_crawler');
const { getKeyManager } = require('../scrape_worker/key_manager');
const { SitemapParser } = require('../scrape_worker/sitemap_parser');
const { db, websiteConfigs, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { count, eq } = require('drizzle-orm');

async function crawlThreeWebsites() {
  console.log('🕷️  Crawling 3 Websites with Improved URL Patterns\n');
  
  try {
    // Get initial queue status
    const initialDiscovered = await db.select({ count: count() }).from(discoveredUrls);
    const initialQueue = await db.select({ count: count() }).from(scrapingQueue);
    
    console.log('📊 Initial Queue Status:');
    console.log(`   🔍 Discovered URLs: ${initialDiscovered[0].count}`);
    console.log(`   📋 Scraping Queue: ${initialQueue[0].count}\n`);
    
    // Initialize components
    const keyManager = getKeyManager();
    const sitemapParser = new SitemapParser();
    const crawler = new SmartCrawler(keyManager, sitemapParser);
    
    const websitesToCrawl = ['betterplace', 'bali_home_immo', 'villabalisale.com'];
    
    for (const websiteId of websitesToCrawl) {
      console.log('='.repeat(80));
      console.log(`🌐 Crawling ${websiteId}`);
      console.log('='.repeat(80));
      
      try {
        const result = await crawler.startWebsiteCrawl(websiteId);
        
        if (result.success) {
          console.log(`✅ ${websiteId} crawled successfully:`);
          console.log(`   📊 Type: ${result.type}`);
          console.log(`   🔍 Total discovered: ${result.totalDiscovered}`);
          console.log(`   ✨ New URLs added: ${result.newUrlsAdded}`);
          console.log(`   🗺️  Sitemaps processed: ${result.sitemapsProcessed || 0}`);
        } else {
          console.log(`❌ ${websiteId} crawling failed: ${result.error}`);
        }
        
      } catch (error) {
        console.log(`❌ Error crawling ${websiteId}: ${error.message}`);
      }
      
      // Wait between websites
      if (websitesToCrawl.indexOf(websiteId) < websitesToCrawl.length - 1) {
        console.log('\n⏳ Waiting 10 seconds before next website...');
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
    
    // Get final queue status
    const finalDiscovered = await db.select({ count: count() }).from(discoveredUrls);
    const finalQueue = await db.select({ count: count() }).from(scrapingQueue);
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 FINAL CRAWLING RESULTS');
    console.log('='.repeat(80));
    
    console.log('\n📊 Database Status After Crawling:');
    console.log(`   🔍 Discovered URLs: ${finalDiscovered[0].count} (was ${initialDiscovered[0].count})`);
    console.log(`   📋 Scraping Queue: ${finalQueue[0].count} (was ${initialQueue[0].count})`);
    
    const newDiscovered = finalDiscovered[0].count - initialDiscovered[0].count;
    const newQueued = finalQueue[0].count - initialQueue[0].count;
    
    console.log('\n✨ New URLs Added:');
    console.log(`   🔍 Discovered: +${newDiscovered}`);
    console.log(`   📋 Queued: +${newQueued}`);
    
    console.log('\n✅ 3 websites crawled with improved URL patterns!');
    console.log('🎯 Ready to test property scraping with better filtering');
    
  } catch (error) {
    console.error('❌ Error during crawling:', error.message);
    process.exit(1);
  } finally {
    closeConnection();
  }
}

crawlThreeWebsites();
