// Debug the actual scraped data for the problematic property
require('dotenv').config();
const { getKeyManager } = require('../scrape_worker/key_manager');

const keyManager = getKeyManager();

async function debugScrapedData() {
  console.log('🔍 Debugging Scraped Data for Problematic Property\n');
  
  // The problematic URL
  const url = 'https://balivillarealty.com/property/brand-new-1-bedroom-villa-for-sale-leasehold-in-bali-tumbak-bayuh/';
  
  console.log(`🌐 URL: ${url}`);
  console.log(`📋 Expected: RESIDENTIAL | VILLA`);
  console.log(`❌ Actual: COMMERCIAL | RETAIL`);
  
  try {
    const currentKey = keyManager.getCurrentKey();
    console.log(`\n🔑 Using key: ${currentKey.maskedKey}`);
    
    // Test with batch scrape API
    console.log('\n🚀 Starting batch scrape...');
    
    const response = await fetch('https://api.firecrawl.dev/v1/batch/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${currentKey.key}`
      },
      body: JSON.stringify({
        urls: [url],
        formats: ['json', 'markdown'],
        jsonOptions: {
          prompt: "Extract property details: title, price, location, bedrooms, bathrooms, description, images (array), property_id, detail_url, property_type, status, size: {land_size_sqm, building_size_sqm}, amenities (array), year_built, parking (parking information like '2 car garage', 'open parking', 'covered parking')"
        },
        onlyMainContent: true,
        timeout: 30000,
        maxConcurrency: 1,
        ignoreInvalidURLs: true,
        blockAds: true,
        proxy: 'auto',
        waitFor: 2000,
        removeBase64Images: true
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Batch scrape failed: HTTP ${response.status}`);
      console.log(`Error: ${errorText}`);
      return;
    }
    
    const batchData = await response.json();
    console.log(`✅ Batch scrape initiated successfully!`);
    console.log(`Job ID: ${batchData.id}`);
    
    // Poll for results
    console.log('\n⏳ Polling for results...');
    const jobId = batchData.id;
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 15000)); // Wait 15 seconds
      attempts++;
      
      console.log(`📊 Checking status (attempt ${attempts}/${maxAttempts})...`);
      
      const statusResponse = await fetch(`https://api.firecrawl.dev/v1/batch/scrape/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${currentKey.key}`
        }
      });
      
      if (!statusResponse.ok) {
        console.log(`⚠️  Status check failed: ${statusResponse.status}`);
        continue;
      }
      
      const statusData = await statusResponse.json();
      console.log(`Status: ${statusData.status}, Completed: ${statusData.completed}/${statusData.total}`);
      
      if (statusData.status === 'completed') {
        console.log('\n🎉 Batch scrape completed!');
        
        if (statusData.data && statusData.data.length > 0) {
          const result = statusData.data[0];
          
          console.log('\n📄 Raw Scraped Data:');
          console.log('='.repeat(50));
          
          if (result.json) {
            console.log('📊 JSON Data:');
            console.log(JSON.stringify(result.json, null, 2));
            
            // Test classification with this data
            console.log('\n🧪 Testing Classification:');
            testClassification(result.json);
          }
          
          if (result.markdown) {
            console.log('\n📝 Markdown Data (first 500 chars):');
            console.log(result.markdown.substring(0, 500) + '...');
          }
          
        } else {
          console.log('❌ No data returned in results');
        }
        
        break;
      } else if (statusData.status === 'failed') {
        console.log('❌ Batch scrape failed');
        break;
      }
    }
    
    if (attempts >= maxAttempts) {
      console.log('⏰ Polling timeout - job may still be running');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

function testClassification(scrapedData) {
  console.log('\n🔍 Classification Analysis:');
  
  // Extract the fields that might be used for classification
  const title = scrapedData.title || '';
  const description = scrapedData.description || '';
  const property_type = scrapedData.property_type || '';
  const status = scrapedData.status || '';
  
  console.log(`📝 Title: "${title}"`);
  console.log(`📝 Description: "${description}"`);
  console.log(`📝 Property Type: "${property_type}"`);
  console.log(`📝 Status: "${status}"`);
  
  // Test the classification function from mappers.js
  const { classifyProperty } = require('../scrape_worker/mappers');
  
  if (typeof classifyProperty === 'function') {
    const result = classifyProperty(title, description);
    console.log(`\n🎯 JavaScript Classification: ${result.category} | ${result.type}`);
  } else {
    console.log('\n⚠️  classifyProperty function not found, testing manually...');
    
    // Manual classification test
    const text = `${title} ${description}`.toLowerCase();
    console.log(`\n🔍 Combined text: "${text}"`);
    
    // Check for problematic keywords
    const problematicWords = ['shop', 'office', 'retail', 'commercial', 'store', 'business'];
    const foundWords = [];
    
    problematicWords.forEach(word => {
      if (text.includes(word)) {
        const index = text.indexOf(word);
        foundWords.push({
          word,
          index,
          context: text.substring(Math.max(0, index-10), index+word.length+10)
        });
      }
    });
    
    if (foundWords.length > 0) {
      console.log('\n❌ Problematic words found:');
      foundWords.forEach(found => {
        console.log(`   "${found.word}" at position ${found.index}: "${found.context}"`);
      });
    } else {
      console.log('\n✅ No problematic words found');
    }
    
    // Check for villa
    if (text.includes('villa')) {
      console.log('✅ "villa" found - should be RESIDENTIAL | VILLA');
    } else {
      console.log('❌ "villa" not found');
    }
  }
}

debugScrapedData();
