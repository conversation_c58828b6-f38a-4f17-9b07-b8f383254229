require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testBPVF00748Extraction() {
  console.log('🧪 TESTING BPVF00748 EXTRACTION');
  console.log('='.repeat(60));

  const url = 'https://betterplace.cc/buy/properties/BPVF00748';
  console.log(`🔍 Testing: ${url}`);
  console.log(`📋 Expected: 3 bedrooms (from title "3 Bedroom Villa"), unknown bathrooms`);

  try {
    const result = await runExtractBatch('betterplace', [url], {});

    if (result && result.extractedData && result.extractedData.length > 0) {
      const data = result.extractedData[0];

      console.log(`\n✅ EXTRACTION RESULT:`);
      console.log(`   Title: ${data.title}`);
      console.log(`   Bedrooms: ${data.bedrooms} ${data.bedrooms === 3 ? '✅' : '❌'}`);
      console.log(`   Bathrooms: ${data.bathrooms} ${data.bathrooms ? '✅' : '❌'}`);
      console.log(`   Property Type: ${data.property_type}`);
      console.log(`   Year Built: ${data.year_built}`);

      // Analyze the problem
      if (data.bedrooms !== 3) {
        console.log(`\n❌ BEDROOM PROBLEM:`);
        console.log(`   Expected: 3 (from title "3 Bedroom Villa")`);
        console.log(`   Got: ${data.bedrooms}`);
        console.log(`   This suggests the bedroom extraction is not working correctly`);
      }

      if (!data.bathrooms) {
        console.log(`\n❌ BATHROOM PROBLEM:`);
        console.log(`   Expected: Some number`);
        console.log(`   Got: ${data.bathrooms}`);
        console.log(`   This suggests the bathroom extraction is not finding any count`);
      }

      if (data.bedrooms === 3 && data.bathrooms) {
        console.log(`\n🎉 SUCCESS! Extraction works correctly!`);
      } else {
        console.log(`\n❌ EXTRACTION STILL HAS ISSUES`);
      }

    } else {
      console.log('❌ No data extracted');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testBPVF00748Extraction();
