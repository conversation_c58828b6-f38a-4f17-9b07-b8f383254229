require('dotenv').config();

async function debugBathroomPattern() {
  console.log('🔍 DEBUGGING BATHROOM PATTERN');
  
  // Test with the exact markdown from the website
  const testMarkdown = `
## Details

*   BPVL00910Property ID
    
*   VillaProperty type
    
*   Umalas
    
*   3
    
    4
    
*   LeaseholdOwnership type
    
*   2018Year built
    
*   17 YearsLease
    
*   Q4 2042Lease Expiry
    
*   600 sqmLand size
    
*   400 sqmBuilding size

![bedrooms](/_next/static/media/bedrooms.7a6788f7.svg)3

![bathrooms](/_next/static/media/bathrooms.45d31171.svg)4
  `;
  
  console.log('🔍 Testing patterns on real markdown:');
  
  // Test the current fallback pattern
  const fallbackPattern = /!\[bathrooms?\]\([^)]+\)([1-9])/i;
  const fallbackMatch = testMarkdown.match(fallbackPattern);
  console.log(`Fallback pattern: ${fallbackMatch ? `Found "${fallbackMatch[1]}"` : 'NOT FOUND'}`);
  
  // Test the CSS pattern in Details section
  const detailsSection = testMarkdown.match(/## Details([\s\S]*?)(?=##|$)/);
  if (detailsSection) {
    console.log('✅ Details section found');
    const cssPattern = /bathrooms\.45d31171\.svg.*?(\d+)/;
    const cssMatch = detailsSection[1].match(cssPattern);
    console.log(`CSS pattern in Details: ${cssMatch ? `Found "${cssMatch[1]}"` : 'NOT FOUND'}`);
    
    // Test the fallback pattern in Details section
    const fallbackInDetails = detailsSection[1].match(fallbackPattern);
    console.log(`Fallback in Details: ${fallbackInDetails ? `Found "${fallbackInDetails[1]}"` : 'NOT FOUND'}`);
  }
  
  // Test a simpler pattern
  const simplePattern = /bathrooms.*?(\d+)/i;
  const simpleMatch = testMarkdown.match(simplePattern);
  console.log(`Simple pattern: ${simpleMatch ? `Found "${simpleMatch[1]}"` : 'NOT FOUND'}`);
  
  // Test the exact structure we see
  const exactPattern = /!\[bathrooms\]\([^)]+\)(\d+)/;
  const exactMatch = testMarkdown.match(exactPattern);
  console.log(`Exact pattern: ${exactMatch ? `Found "${exactMatch[1]}"` : 'NOT FOUND'}`);
}

debugBathroomPattern();
