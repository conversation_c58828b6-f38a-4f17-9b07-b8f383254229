require('dotenv').config();
const { Pool } = require('pg');

async function testSpecificProperty() {
  console.log('🔍 TESTING BPVF00748 BEDROOM/BATHROOM EXTRACTION');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get the specific property from database
    const result = await pool.query(`
      SELECT 
        id,
        external_id,
        title,
        bedrooms,
        bathrooms,
        category,
        type,
        created_at,
        last_scraped_at,
        source_url
      FROM property 
      WHERE id = '76b77233-758c-490d-ae06-9ec6b2a0c073'
         OR external_id = 'BPVF00748'
         OR source_url LIKE '%BPVF00748%'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ Property not found in database');
      return;
    }
    
    const property = result.rows[0];
    
    console.log(`📊 DATABASE PROPERTY:`);
    console.log(`   ID: ${property.id}`);
    console.log(`   External ID: ${property.external_id}`);
    console.log(`   Title: ${property.title}`);
    console.log(`   Bedrooms: ${property.bedrooms} ${property.bedrooms ? '✅' : '❌'}`);
    console.log(`   Bathrooms: ${property.bathrooms} ${property.bathrooms ? '✅' : '❌'}`);
    console.log(`   Category: ${property.category}`);
    console.log(`   Type: ${property.type}`);
    console.log(`   Last Scraped: ${property.last_scraped_at}`);
    console.log(`   URL: ${property.source_url}`);
    
    // Check if this was scraped recently
    const scrapedRecently = property.last_scraped_at && new Date(property.last_scraped_at) > new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    console.log(`\n📅 TIMING:`);
    console.log(`   Scraped in last 24h: ${scrapedRecently ? '✅ Yes' : '❌ No'}`);
    
    if (!scrapedRecently) {
      console.log(`\n🎯 LIKELY CAUSE: Property needs to be re-scraped with the new extraction logic`);
    } else {
      console.log(`\n❌ PROBLEM: Recently scraped but still has wrong bedroom/bathroom count`);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

testSpecificProperty();
