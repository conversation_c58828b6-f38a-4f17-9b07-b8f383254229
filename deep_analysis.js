require('dotenv').config();
const { Pool } = require('pg');

async function deepAnalysis() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🔍 DEEP ANALYSIS: URL vs DATABASE COMPARISON');
    console.log('='.repeat(60));
    
    // Get 5 recent properties with bedroom/bathroom data
    const result = await pool.query(`
      SELECT 
        external_id,
        title,
        bedrooms,
        bathrooms,
        source_url,
        created_at
      FROM property 
      WHERE bedrooms IS NOT NULL 
        AND bathrooms IS NOT NULL
        AND source_url LIKE '%betterplace.cc%'
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    console.log(`📊 Found ${result.rows.length} properties to analyze:\n`);
    
    result.rows.forEach((property, index) => {
      console.log(`${index + 1}. ${property.external_id}:`);
      console.log(`   📍 URL: ${property.source_url}`);
      console.log(`   🏠 Database: ${property.bedrooms} bed / ${property.bathrooms} bath`);
      console.log(`   📝 Title: ${property.title}`);
      console.log(`   📅 Created: ${property.created_at}`);
      console.log('');
    });
    
    console.log('🎯 NEXT STEPS:');
    console.log('1. Manually check these URLs in browser');
    console.log('2. Compare actual bedroom/bathroom counts');
    console.log('3. Test CSS extraction patterns');
    console.log('4. Identify root cause');
    
    // Return the URLs for further testing
    return result.rows;
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

deepAnalysis();
