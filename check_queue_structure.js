require('dotenv').config();
const { Pool } = require('pg');

async function checkQueueStructure() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🔍 Checking scraping_queue table structure...');
    
    // Check table structure
    const columnsResult = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'scraping_queue'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 Table columns:');
    columnsResult.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // Check if the problematic columns exist
    const problemColumns = ['status', 'scheduled_for', 'website_id'];
    console.log('\n🔍 Checking problematic columns:');
    problemColumns.forEach(col => {
      const exists = columnsResult.rows.find(row => row.column_name === col);
      console.log(`  - ${col}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
    });
    
    // Check current queue content
    const queueResult = await pool.query(`
      SELECT COUNT(*) as total_items
      FROM scraping_queue
    `);
    
    console.log(`\n📊 Total items in queue: ${queueResult.rows[0].total_items}`);
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkQueueStructure();
