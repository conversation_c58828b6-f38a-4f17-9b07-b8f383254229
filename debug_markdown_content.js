require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugMarkdownContent() {
  console.log('🔍 DEBUGGING MARKDOWN CONTENT FOR BATHROOM ICONS');
  console.log('='.repeat(80));

  const url = 'https://betterplace.cc/buy/properties/BPVL00682';
  console.log(`🔍 URL: ${url}`);

  try {
    console.log('\n🕷️ SCRAPING TO GET RAW MARKDOWN...');

    // Use a modified version that returns the raw markdown
    const { FirecrawlKeyManager } = require('./scrape_worker/key_manager');
    const keyManager = new FirecrawlKeyManager();

    // Scrape the URL directly
    const crawlResult = await keyManager.scrapeUrl(url, {
      formats: ['markdown'],
      timeout: 30000
    });

    if (!crawlResult?.data?.markdown) {
      console.log('❌ No markdown data received');
      return;
    }

    const markdown = crawlResult.data.markdown;
    console.log(`📄 Markdown length: ${markdown.length} chars`);
    
    // Find Details section
    const detailsSection = markdown.match(/## Details([\s\S]*?)(?=##|$)/);
    if (detailsSection) {
      console.log(`\n✅ Found Details section (${detailsSection[1].length} chars)`);
      
      // Show the Details section content
      console.log('\n📋 DETAILS SECTION CONTENT:');
      console.log('='.repeat(60));
      console.log(detailsSection[1].substring(0, 2000)); // First 2000 chars
      console.log('='.repeat(60));
      
      // Search for bathroom-related content
      console.log('\n🔍 SEARCHING FOR BATHROOM PATTERNS:');
      
      const patterns = [
        { name: 'bathroom icon markdown', pattern: /!\[bathrooms?\]\([^)]+\)(\d+)/gi },
        { name: 'bathroom svg reference', pattern: /bathrooms\.45d31171\.svg/gi },
        { name: 'mask-image bathroom', pattern: /mask-image:url\([^)]*bathrooms[^)]*\)/gi },
        { name: 'bathroom word', pattern: /bathroom/gi },
        { name: 'bath word', pattern: /\bbath\b/gi },
        { name: 'any number after bathroom', pattern: /bathroom[^0-9]*(\d+)/gi },
        { name: 'any number before bathroom', pattern: /(\d+)[^0-9]*bathroom/gi }
      ];
      
      patterns.forEach(({ name, pattern }) => {
        const matches = [...detailsSection[1].matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`   ✅ ${name}: Found ${matches.length} matches`);
          matches.forEach((match, i) => {
            console.log(`      Match ${i + 1}: "${match[0]}"`);
            if (match[1]) console.log(`         Captured number: ${match[1]}`);
          });
        } else {
          console.log(`   ❌ ${name}: No matches`);
        }
      });
      
      // Look for any numbers in the Details section
      console.log('\n🔢 ALL NUMBERS IN DETAILS SECTION:');
      const numberMatches = [...detailsSection[1].matchAll(/\d+/g)];
      numberMatches.forEach((match, i) => {
        const context = detailsSection[1].substring(Math.max(0, match.index - 50), match.index + 50);
        console.log(`   ${i + 1}. Number "${match[0]}" in context: "...${context}..."`);
      });
      
    } else {
      console.log('❌ No Details section found');
    }
    
    // Also search in the entire markdown
    console.log('\n🔍 SEARCHING ENTIRE MARKDOWN FOR BATHROOM CONTENT:');
    const bathroomMatches = [...markdown.matchAll(/bathroom/gi)];
    console.log(`Found ${bathroomMatches.length} occurrences of "bathroom" in entire markdown`);
    
    bathroomMatches.slice(0, 5).forEach((match, i) => {
      const context = markdown.substring(Math.max(0, match.index - 100), match.index + 100);
      console.log(`   ${i + 1}. "bathroom" in context: "...${context}..."`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugMarkdownContent();
