require('dotenv').config();

// Temporarily modify the BetterPlace mapper to show raw markdown
const originalParseBetterPlaceMarkdown = require('./scrape_worker/mappers').parseBetterPlaceMarkdown;

// Override the function to debug markdown content
function debugParseBetterPlaceMarkdown(markdown, url) {
  console.log('🔍 DEBUGGING RAW MARKDOWN CONTENT');
  console.log('='.repeat(80));
  console.log(`📄 Markdown length: ${markdown.length} chars`);
  
  // Find Details section
  const detailsSection = markdown.match(/## Details([\s\S]*?)(?=##|$)/);
  if (detailsSection) {
    console.log(`\n✅ Found Details section (${detailsSection[1].length} chars)`);
    
    // Show the Details section content
    console.log('\n📋 DETAILS SECTION CONTENT:');
    console.log('='.repeat(60));
    console.log(detailsSection[1].substring(0, 2000)); // First 2000 chars
    console.log('='.repeat(60));
    
    // Search for bathroom-related content
    console.log('\n🔍 SEARCHING FOR BATHROOM PATTERNS:');
    
    const patterns = [
      { name: 'bathroom icon markdown', pattern: /!\[bathrooms?\]\([^)]+\)(\d+)/gi },
      { name: 'bathroom svg reference', pattern: /bathrooms\.45d31171\.svg/gi },
      { name: 'mask-image bathroom', pattern: /mask-image:url\([^)]*bathrooms[^)]*\)/gi },
      { name: 'bathroom word', pattern: /bathroom/gi },
      { name: 'bath word', pattern: /\bbath\b/gi },
      { name: 'any number after bathroom', pattern: /bathroom[^0-9]*(\d+)/gi },
      { name: 'any number before bathroom', pattern: /(\d+)[^0-9]*bathroom/gi }
    ];
    
    patterns.forEach(({ name, pattern }) => {
      const matches = [...detailsSection[1].matchAll(pattern)];
      if (matches.length > 0) {
        console.log(`   ✅ ${name}: Found ${matches.length} matches`);
        matches.forEach((match, i) => {
          console.log(`      Match ${i + 1}: "${match[0]}"`);
          if (match[1]) console.log(`         Captured number: ${match[1]}`);
        });
      } else {
        console.log(`   ❌ ${name}: No matches`);
      }
    });
    
    // Look for any numbers in the Details section
    console.log('\n🔢 ALL NUMBERS IN DETAILS SECTION:');
    const numberMatches = [...detailsSection[1].matchAll(/\d+/g)];
    numberMatches.forEach((match, i) => {
      const context = detailsSection[1].substring(Math.max(0, match.index - 50), match.index + 50);
      console.log(`   ${i + 1}. Number "${match[0]}" in context: "...${context}..."`);
    });
    
  } else {
    console.log('❌ No Details section found');
  }
  
  // Stop here - don't continue with normal processing
  console.log('\n🛑 DEBUG COMPLETE - STOPPING HERE');
  return null;
}

// Replace the function temporarily
require('./scrape_worker/mappers').parseBetterPlaceMarkdown = debugParseBetterPlaceMarkdown;

// Now run the extraction
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugRawMarkdown() {
  console.log('🧪 RUNNING DEBUG EXTRACTION');
  console.log('='.repeat(60));
  
  const url = 'https://betterplace.cc/buy/properties/BPVL00682';
  
  try {
    await runExtractBatch('betterplace', [url], {});
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugRawMarkdown();
