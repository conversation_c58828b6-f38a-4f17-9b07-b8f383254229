#!/usr/bin/env node

// Test the fixes for bedroom/bathroom extraction and API issues
require('dotenv').config();

const { getKeyManager } = require('./scrape_worker/key_manager');
const { mapBaliHomeImmo } = require('./scrape_worker/mappers');

async function testFixes() {
  console.log('🧪 Testing fixes for bedroom/bathroom extraction and API issues...\n');
  
  // Test 1: Key Manager
  console.log('1️⃣ Testing Key Manager...');
  try {
    const keyManager = getKeyManager();
    const keyInfo = keyManager.getCurrentKey();
    console.log(`   ✅ Key Manager working: ${keyInfo.maskedKey}`);
  } catch (error) {
    console.log(`   ❌ Key Manager error: ${error.message}`);
  }
  
  // Test 2: BetterPlace Villa Classification Fix
  console.log('\n2️⃣ Testing BetterPlace Villa Classification Fix...');

  const betterplaceVillaData = {
    markdown: `
# Modern Garden Villa with Private Pool Near Tanah Lot – A Perfect Bali Investment

Price: IDR 4,114,218,240

**Details:**
- 2 bedrooms
- 3 bathrooms
- 150 sqm Land size
- 139 sqm Building size
- Villa Property type
- Leasehold Ownership type
- Private Pool type
    `,
    url: 'https://betterplace.cc/buy/properties/BPVL01794'
  };

  try {
    const { mapBetterPlace } = require('./scrape_worker/mappers');
    const result = await mapBetterPlace(betterplaceVillaData);

    if (result) {
      console.log(`   ✅ Title: ${result.title}`);
      console.log(`   ✅ Category: ${result.category} (should be RESIDENTIAL)`);
      console.log(`   ✅ Type: ${result.type} (should be VILLA)`);
      console.log(`   ✅ Bedrooms: ${result.bedrooms} (should be 2)`);
      console.log(`   ✅ Bathrooms: ${result.bathrooms} (should be 3)`);

      if (result.category === 'RESIDENTIAL' && result.type === 'VILLA') {
        console.log(`   ✅ VILLA CLASSIFICATION WORKING!`);
      } else {
        console.log(`   ❌ VILLA CLASSIFICATION NOT WORKING! Got: ${result.category}/${result.type}`);
      }
    } else {
      console.log(`   ❌ Mapping returned null`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  // Test 3: Rent Duration Detection
  console.log('\n3️⃣ Testing Rent Duration Detection...');

  const rentDurationTests = [
    {
      title: 'Villa for Monthly Rent in Canggu',
      description: 'Beautiful villa available for rent per month',
      url: 'https://example.com/monthly-rental',
      priceText: '',
      expected: 'MONTH'
    },
    {
      title: 'Yearly Rental Villa in Ubud',
      description: 'Long term rental available per year',
      url: 'https://example.com/yearly-rental',
      priceText: '',
      expected: 'YEAR'
    },
    {
      title: 'Daily Villa Rental in Seminyak',
      description: 'Short stay villa rental per day',
      url: 'https://example.com/daily-rental',
      priceText: '',
      expected: 'DAY'
    },
    {
      title: 'BRAND NEW WITH OCEAN VIEW 3 BEDROOMS VILLA FOR MONTHLY & YEARLY RENTAL IN BALI - UNGASAN - AD060',
      description: 'Available for both monthly and yearly rental',
      url: 'https://example.com/monthly-yearly-rental',
      priceText: 'IDR 50,000,000/month',
      expected: 'MONTH'
    },
    {
      title: 'Villa for Daily & Monthly Rental',
      description: 'Flexible rental terms available',
      url: 'https://example.com/daily-monthly-rental',
      priceText: 'USD 200 per day',
      expected: 'DAY'
    },
    {
      title: 'Villa for Monthly & Yearly Rental',
      description: 'Both options available',
      url: 'https://example.com/ambiguous',
      priceText: 'Price: USD 15,000 per year',
      expected: 'YEAR'
    }
  ];

  const { detectRentDuration } = require('./scrape_worker/mappers');

  rentDurationTests.forEach((test, i) => {
    const result = detectRentDuration(test.title, test.description, test.url, test.priceText);
    console.log(`   ${i+1}. "${test.title}" → ${result} (expected: ${test.expected})`);
    if (test.priceText) {
      console.log(`      💰 Price: "${test.priceText}"`);
    }
    if (result === test.expected) {
      console.log(`      ✅ Correct`);
    } else {
      console.log(`      ❌ Wrong`);
    }
  });

  // Test 4: All Mappers Rent Duration Support
  console.log('\n4️⃣ Testing All Mappers Rent Duration Support...');

  const mapperTests = [
    {
      name: 'BetterPlace',
      mapper: 'mapBetterPlace',
      data: {
        markdown: `# Villa for Monthly Rent\nPrice: IDR 750,000,000`,
        url: 'https://betterplace.cc/rent/villa-monthly'
      },
      expectedDuration: 'MONTH'
    },
    {
      name: 'Bali Villa Realty',
      mapper: 'mapBaliVillaRealty',
      data: {
        title: 'Villa for Yearly Rental',
        price: 'USD 12,000',
        location: 'Ubud, Bali',
        description: 'Beautiful villa for yearly rental'
      },
      url: 'https://balivillarealty.com/for-rent/villa-yearly',
      expectedDuration: 'YEAR'
    }
  ];

  for (const test of mapperTests) {
    console.log(`\n   🧪 Testing: ${test.name}`);

    try {
      const { [test.mapper]: mapperFunction } = require('./scrape_worker/mappers');
      const testData = test.data.url ? { ...test.data, url: test.data.url } : test.data;
      const result = await mapperFunction(testData);

      if (result) {
        console.log(`      Rent Price: ${result.rent_price || 'null'}`);
        console.log(`      Rent Duration: ${result.rent_duration || 'null'} (expected: ${test.expectedDuration})`);

        if (result.rent_duration === test.expectedDuration) {
          console.log(`      ✅ Rent Duration correct`);
        } else {
          console.log(`      ❌ Rent Duration wrong`);
        }
      } else {
        console.log(`      ❌ Mapping returned null`);
      }
    } catch (error) {
      console.log(`      ❌ Error: ${error.message}`);
    }
  }

  // Test 5: Rent Price Fix for BetterPlace
  console.log('\n5️⃣ Testing Rent Price Fix for BetterPlace...');

  const testRentalData = {
    markdown: `
# Modern 2-Bedroom Villa for Rent in Canggu

Price: IDR 750,000,000

**Location:** Canggu, Bali
**Details:**
- 2 bedrooms
- 2 bathrooms
- Swimming pool, Garden
    `,
    url: 'https://betterplace.co.id/property/villa-for-rent-canggu'
  };

  try {
    const { mapBetterPlace } = require('./scrape_worker/mappers');
    const result = await mapBetterPlace(testRentalData);

    if (result) {
      console.log(`   ✅ Title: ${result.title}`);
      console.log(`   ✅ Sale Price: ${result.price || 'null'} (should be null for rental)`);
      console.log(`   ✅ Rent Price: ${result.rent_price || 'null'} (should be 750000000)`);
      console.log(`   ✅ Rent Duration: ${result.rent_duration || 'null'} (should be MONTH)`);

      if (result.price === undefined && result.rent_price === 750000000 && result.rent_duration === 'MONTH') {
        console.log(`   ✅ RENT PRICE FIX WORKING!`);
      } else {
        console.log(`   ❌ RENT PRICE FIX NOT WORKING!`);
      }
    } else {
      console.log(`   ❌ Mapping returned null`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  // Test 6: Property Type Specific Extraction
  console.log('\n6️⃣ Testing Property Type Specific Extraction...');

  const testCases = [
    {
      name: 'Residential Villa',
      markdown: `
# 2-Bedroom Villa for Monthly Rental in Canggu Berawa - VB109
**Price:** IDR 77,000,000/month
**Details:**
- 2 bedrooms
- 2 bathrooms
- Building size: 150 sqm
- Swimming pool, Garden, Parking
      `,
      expectedBedrooms: 2,
      expectedBathrooms: 2,
      expectedAmenities: true
    },
    {
      name: 'Land Property',
      markdown: `
# Prime Land for Sale in Canggu - 500 sqm
**Price:** IDR 2,500,000,000
**Details:**
- Land size: 500 sqm
- 3 bedrooms mentioned in description (should be ignored)
- Swimming pool area (should be ignored)
      `,
      expectedBedrooms: null,
      expectedBathrooms: null,
      expectedAmenities: false
    },
    {
      name: 'Office Space',
      markdown: `
# Office Space for Rent in Seminyak - 50 sqm
**Price:** IDR 25,000,000/month
**Details:**
- Building size: 50 sqm
- 2 bedrooms mentioned (should be ignored)
- Air conditioning, Parking
      `,
      expectedBedrooms: null,
      expectedBathrooms: null,
      expectedAmenities: true
    },
    {
      name: 'Hotel Property',
      markdown: `
# 20-Bedroom Hotel for Sale in Ubud
**Price:** IDR 15,000,000,000
**Details:**
- 20 bedrooms
- 20 bathrooms
- Building size: 800 sqm
- Swimming pool, Restaurant
      `,
      expectedBedrooms: 20,
      expectedBathrooms: 20,
      expectedAmenities: true
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n   🧪 Testing: ${testCase.name}`);

    try {
      const result = await mapBaliHomeImmo({
        markdown: testCase.markdown,
        url: 'https://bali-home-immo.com/test-property'
      });

      if (result) {
        console.log(`      Bedrooms: ${result.bedrooms} (expected: ${testCase.expectedBedrooms})`);
        console.log(`      Bathrooms: ${result.bathrooms} (expected: ${testCase.expectedBathrooms})`);
        console.log(`      Amenities: ${JSON.stringify(result.amenities)} (expected: ${testCase.expectedAmenities ? '>0' : '0'})`);
        console.log(`      Property Type: ${result.type} (category: ${result.category})`);

        // Validate results
        if (result.bedrooms === testCase.expectedBedrooms) {
          console.log(`      ✅ Bedrooms correct`);
        } else {
          console.log(`      ❌ Bedrooms wrong: got ${result.bedrooms}, expected ${testCase.expectedBedrooms}`);
        }

        if (result.bathrooms === testCase.expectedBathrooms) {
          console.log(`      ✅ Bathrooms correct`);
        } else {
          console.log(`      ❌ Bathrooms wrong: got ${result.bathrooms}, expected ${testCase.expectedBathrooms}`);
        }

        const hasAmenities = (result.amenities?.raw_amenities?.length || 0) > 0;
        if (hasAmenities === testCase.expectedAmenities) {
          console.log(`      ✅ Amenities correct`);
        } else {
          console.log(`      ❌ Amenities wrong: got ${hasAmenities}, expected ${testCase.expectedAmenities}`);
        }
      } else {
        console.log(`      ❌ Mapping returned null`);
      }
    } catch (error) {
      console.log(`      ❌ Error: ${error.message}`);
    }
  }
  
  console.log('\n✅ Test completed!');
}

testFixes().catch(console.error);
