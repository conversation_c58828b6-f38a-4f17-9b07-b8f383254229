// Fix incorrect currency rates in database
require('dotenv').config();
const postgres = require('postgres');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

async function fixCurrencyRates() {
  console.log('🔧 Fixing Currency Rates in Database\n');
  
  try {
    // Check current USD to IDR rates
    console.log('📊 Current USD to IDR rates:');
    const usdToIdr = await client`
      SELECT id, from_currency, to_currency, rate, date, created_at
      FROM exchange_rates 
      WHERE from_currency = 'USD' AND to_currency = 'IDR'
      ORDER BY date DESC
    `;
    
    console.table(usdToIdr.map(rate => ({
      'ID': rate.id.substring(0, 8) + '...',
      'Rate': rate.rate,
      'Date': new Date(rate.date).toLocaleDateString(),
      'Created': new Date(rate.created_at).toLocaleString()
    })));
    
    // The problem: there's a newer rate (16-8-2025) with value 15800 that's incorrect
    // The correct rate is from 11-8-2025 with value 16235.664944
    
    console.log('\n🔍 Analysis:');
    console.log('❌ Rate 15800 (16-8-2025) - This looks like a fallback value, not a real rate');
    console.log('✅ Rate 16235.664944 (11-8-2025) - This looks like a real exchange rate');
    
    // Find the incorrect rate (15800 from 16-8-2025)
    const incorrectRate = await client`
      SELECT id, rate, date
      FROM exchange_rates 
      WHERE from_currency = 'USD' 
        AND to_currency = 'IDR' 
        AND rate = 15800.000000
        AND date >= '2025-08-16'
    `;
    
    if (incorrectRate.length > 0) {
      console.log(`\n🗑️  Found incorrect rate to remove: ${incorrectRate[0].rate} (${new Date(incorrectRate[0].date).toLocaleDateString()})`);
      
      // Ask for confirmation (simulate user input)
      console.log('🤔 This rate (15800) appears to be a fallback value, not a real exchange rate.');
      console.log('The correct current rate should be around 16235 based on the previous entry.');
      console.log('\n🔄 Removing incorrect rate...');
      
      // Remove the incorrect rate
      await client`
        DELETE FROM exchange_rates 
        WHERE id = ${incorrectRate[0].id}
      `;
      
      console.log('✅ Incorrect rate removed!');
      
      // Verify the fix
      console.log('\n📊 Updated USD to IDR rates:');
      const updatedRates = await client`
        SELECT rate, date
        FROM exchange_rates 
        WHERE from_currency = 'USD' AND to_currency = 'IDR'
        ORDER BY date DESC
        LIMIT 3
      `;
      
      console.table(updatedRates.map(rate => ({
        'Rate': rate.rate,
        'Date': new Date(rate.date).toLocaleDateString()
      })));
      
      // Test the currency service after fix
      console.log('\n🧪 Testing Currency Service after fix:');
      const { CurrencyService } = require('../scrape_worker/currency_service');
      const currencyService = new CurrencyService();
      
      // Clear cache to force fresh lookup
      currencyService.rateCache.clear();
      
      const rate = await currencyService.getExchangeRate('USD', 'IDR');
      console.log(`New USD→IDR rate: ${rate}`);
      
      // Test conversion
      console.log('\n💵 Testing $165,000 conversion with corrected rate:');
      const usdAmount = 165000;
      const idrAmount = usdAmount * rate;
      console.log(`$${usdAmount.toLocaleString()} = IDR ${idrAmount.toLocaleString()}`);
      
      if (rate > 16000) {
        console.log('✅ Currency rate is now correct!');
      } else {
        console.log('⚠️  Rate still seems low, may need manual update');
      }
      
    } else {
      console.log('\n✅ No incorrect rates found to remove');
    }
    
    // Show summary of all major currency rates
    console.log('\n📊 Summary of Major Currency Rates:');
    const majorRates = await client`
      SELECT DISTINCT ON (from_currency, to_currency) 
        from_currency, to_currency, rate, date
      FROM exchange_rates 
      WHERE to_currency = 'IDR' 
        AND from_currency IN ('USD', 'EUR', 'SGD', 'AUD', 'GBP')
      ORDER BY from_currency, to_currency, date DESC
    `;
    
    console.table(majorRates.map(rate => ({
      'From': rate.from_currency,
      'To': rate.to_currency,
      'Rate': rate.rate,
      'Date': new Date(rate.date).toLocaleDateString()
    })));
    
  } catch (error) {
    console.error('❌ Error fixing currency rates:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

fixCurrencyRates();
