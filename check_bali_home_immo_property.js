require('dotenv').config();
const { Pool } = require('pg');

async function checkBaliHomeImmoProperty() {
  console.log('🔍 CHECKING BALI HOME IMMO PROPERTY: 3a59d8f8-6721-4e20-8dbe-9500380c9fe9');
  console.log('='.repeat(80));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get the specific property
    const result = await pool.query(`
      SELECT *
      FROM property
      WHERE id = '3a59d8f8-6721-4e20-8dbe-9500380c9fe9'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ Property not found in database');
      return;
    }
    
    const prop = result.rows[0];
    
    console.log(`📊 DATABASE VALUES:`);
    console.log(`   ID: ${prop.id}`);
    console.log(`   External ID: ${prop.external_id}`);
    console.log(`   Title: ${prop.title}`);
    console.log(`   URL: ${prop.source_url}`);
    console.log(`   Last Scraped: ${prop.last_scraped_at}`);
    console.log('');
    
    // Expected values from the markdown
    const expectedTitle = "Luxury 2 Bedroom Penthouse for rent in Batu Belig - BHI1403C";
    const expectedBedrooms = 2; // From title "2 Bedroom"
    const expectedBathrooms = 2; // From table "Bathroom : 2"
    const expectedYearBuilt = 2023; // From "Year of Build : 2023"
    const expectedLocation = "Batu Belig"; // From title and content
    const expectedLandSize = 158; // From "Land Size : 158 m²"
    const expectedBuildingSize = 158; // From "Building Size : 158 m²"
    const expectedPrice = 68500000; // From "68.500.000 /month"
    const expectedCategory = "RESIDENTIAL";
    const expectedType = "APARTMENT"; // It's a penthouse/apartment
    
    console.log(`📋 FIELD-BY-FIELD ANALYSIS:`);
    console.log('='.repeat(60));
    
    // Title
    console.log(`1. TITLE:`);
    console.log(`   Expected: "${expectedTitle}"`);
    console.log(`   Database: "${prop.title}"`);
    console.log(`   Status: ${prop.title === expectedTitle ? '✅ CORRECT' : '❌ WRONG'}`);
    
    // Bedrooms
    console.log(`\n2. BEDROOMS:`);
    console.log(`   Expected: ${expectedBedrooms} (from title "2 Bedroom")`);
    console.log(`   Database: ${prop.bedrooms}`);
    console.log(`   Status: ${prop.bedrooms === expectedBedrooms ? '✅ CORRECT' : '❌ WRONG'}`);
    
    // Bathrooms
    console.log(`\n3. BATHROOMS:`);
    console.log(`   Expected: ${expectedBathrooms} (from table "Bathroom : 2")`);
    console.log(`   Database: ${prop.bathrooms}`);
    console.log(`   Status: ${prop.bathrooms === expectedBathrooms ? '✅ CORRECT' : '❌ WRONG'}`);
    
    // Year Built
    console.log(`\n4. YEAR BUILT:`);
    console.log(`   Expected: ${expectedYearBuilt} (from "Year of Build : 2023")`);
    console.log(`   Database: ${prop.year_built}`);
    console.log(`   Status: ${prop.year_built === expectedYearBuilt ? '✅ CORRECT' : '❌ WRONG'}`);
    
    // Location
    console.log(`\n5. LOCATION:`);
    console.log(`   Expected: "${expectedLocation}" (from title and content)`);
    console.log(`   Database: "${prop.location}"`);
    console.log(`   Status: ${prop.location && prop.location.includes('Batu Belig') ? '✅ CORRECT' : '❌ WRONG'}`);
    
    // Land Size
    console.log(`\n6. LAND SIZE:`);
    console.log(`   Expected: ${expectedLandSize} sqm (from "Land Size : 158 m²")`);
    console.log(`   Database: ${prop.land_size_sqm}`);
    console.log(`   Status: ${prop.land_size_sqm === expectedLandSize ? '✅ CORRECT' : '❌ WRONG'}`);
    
    // Building Size
    console.log(`\n7. BUILDING SIZE:`);
    console.log(`   Expected: ${expectedBuildingSize} sqm (from "Building Size : 158 m²")`);
    console.log(`   Database: ${prop.building_size_sqm}`);
    console.log(`   Status: ${prop.building_size_sqm === expectedBuildingSize ? '✅ CORRECT' : '❌ WRONG'}`);
    
    // Price
    console.log(`\n8. PRICE:`);
    console.log(`   Expected: ${expectedPrice} IDR (from "68.500.000 /month")`);
    console.log(`   Database: ${prop.price}`);
    console.log(`   Status: ${prop.price === expectedPrice ? '✅ CORRECT' : '❌ WRONG'}`);

    // Category & Type
    console.log(`\n9. CLASSIFICATION:`);
    console.log(`   Expected: ${expectedCategory} ${expectedType} (penthouse = apartment)`);
    console.log(`   Database: ${prop.category} ${prop.type}`);
    console.log(`   Status: ${prop.category === expectedCategory && prop.type === expectedType ? '✅ CORRECT' : '❌ WRONG'}`);

    // Overall assessment
    const issues = [];
    if (prop.title !== expectedTitle) issues.push('Title');
    if (prop.bedrooms !== expectedBedrooms) issues.push('Bedrooms');
    if (prop.bathrooms !== expectedBathrooms) issues.push('Bathrooms');
    if (prop.year_built !== expectedYearBuilt) issues.push('Year Built');
    if (!prop.location || !prop.location.includes('Batu Belig')) issues.push('Location');
    if (prop.land_size_sqm !== expectedLandSize) issues.push('Land Size');
    if (prop.building_size_sqm !== expectedBuildingSize) issues.push('Building Size');
    if (prop.price !== expectedPrice) issues.push('Price');
    if (prop.category !== expectedCategory || prop.type !== expectedType) issues.push('Classification');
    
    console.log(`\n🎯 OVERALL ASSESSMENT:`);
    if (issues.length === 0) {
      console.log(`   ✅ ALL FIELDS CORRECT!`);
    } else {
      console.log(`   ❌ ${issues.length} ISSUES FOUND:`);
      issues.forEach(issue => console.log(`      - ${issue}`));
    }
    
    console.log(`\n📝 NEXT STEPS:`);
    if (issues.length > 0) {
      console.log(`   1. Check the Bali Home Immo mapper patterns`);
      console.log(`   2. Test extraction with the provided markdown`);
      console.log(`   3. Fix the patterns that are not working`);
      console.log(`   4. Re-scrape this property to verify fixes`);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkBaliHomeImmoProperty();
