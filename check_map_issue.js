require('dotenv').config();
const { Pool } = require('pg');

async function checkMapIssue() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🔍 Checking property with "map" in address/city...');
    
    const result = await pool.query(`
      SELECT 
        id,
        external_id,
        title,
        address,
        city,
        bedrooms,
        bathrooms,
        source_url
      FROM property 
      WHERE id = 'e11303a6-14ef-47f4-ae0c-89035f0bd530'
         OR address = 'map'
         OR city = 'map'
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ No properties found with "map" issue');
    } else {
      console.log(`✅ Found ${result.rows.length} properties with "map" issue:`);
      
      result.rows.forEach((property, index) => {
        console.log(`\n${index + 1}. ${property.external_id}:`);
        console.log(`   ID: ${property.id}`);
        console.log(`   Title: ${property.title}`);
        console.log(`   Address: "${property.address}" ${property.address === 'map' ? '❌' : '✅'}`);
        console.log(`   City: "${property.city}" ${property.city === 'map' ? '❌' : '✅'}`);
        console.log(`   Bedrooms: ${property.bedrooms}`);
        console.log(`   Bathrooms: ${property.bathrooms}`);
        console.log(`   URL: ${property.source_url}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkMapIssue();
