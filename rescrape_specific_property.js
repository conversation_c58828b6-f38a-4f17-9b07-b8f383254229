require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function rescrapeSpecificProperty() {
  console.log('🔄 RE-SCRAPING SPECIFIC PROPERTY WITH NEW FIXES');
  console.log('='.repeat(60));
  
  const url = 'https://betterplace.cc/buy/properties/BPVL00682';
  const expectedBedrooms = 3; // From "3BR"
  
  console.log(`🔍 URL: ${url}`);
  console.log(`📋 Expected: 3 bedrooms (from "3BR"), 2-4 bathrooms`);
  
  try {
    console.log('\n🕷️ SCRAPING WITH NEW EXTRACTION LOGIC...');
    
    const result = await runExtractBatch('betterplace', [url], {});
    
    if (result && result.extractedData && result.extractedData.length > 0) {
      const data = result.extractedData[0];
      
      console.log(`\n✅ NEW EXTRACTION RESULTS:`);
      console.log(`   Title: ${data.title}`);
      console.log(`   Bedrooms: ${data.bedrooms} ${data.bedrooms === expectedBedrooms ? '✅' : '❌'}`);
      console.log(`   Bathrooms: ${data.bathrooms} ${data.bathrooms >= 2 && data.bathrooms <= 4 ? '✅' : '❌'}`);
      console.log(`   Category: ${data.category}`);
      console.log(`   Type: ${data.type}`);
      console.log(`   Year Built: ${data.year_built}`);
      
      // Compare with expected values
      console.log(`\n📊 ANALYSIS:`);
      
      if (data.bedrooms === expectedBedrooms) {
        console.log(`   🎉 BEDROOM EXTRACTION: FIXED! (3 bedrooms from "3BR")`);
      } else {
        console.log(`   ❌ BEDROOM EXTRACTION: Still wrong (expected ${expectedBedrooms}, got ${data.bedrooms})`);
      }
      
      if (data.bathrooms >= 2 && data.bathrooms <= 4) {
        console.log(`   🎉 BATHROOM EXTRACTION: REASONABLE! (${data.bathrooms} bathrooms)`);
      } else {
        console.log(`   ❌ BATHROOM EXTRACTION: Still problematic (${data.bathrooms} bathrooms)`);
      }
      
      if (data.category === 'RESIDENTIAL' && data.type === 'VILLA') {
        console.log(`   🎉 CLASSIFICATION: CORRECT! (RESIDENTIAL VILLA)`);
      } else {
        console.log(`   ❌ CLASSIFICATION: Wrong (${data.category} ${data.type})`);
      }
      
      if (data.year_built && data.year_built >= 2000 && data.year_built <= 2025) {
        console.log(`   🎉 YEAR BUILT: REASONABLE! (${data.year_built})`);
      } else {
        console.log(`   ❌ YEAR BUILT: Problematic (${data.year_built})`);
      }
      
      // Overall assessment
      const bedroomOK = data.bedrooms === expectedBedrooms;
      const bathroomOK = data.bathrooms >= 2 && data.bathrooms <= 4;
      const classificationOK = data.category === 'RESIDENTIAL' && data.type === 'VILLA';
      const yearOK = data.year_built && data.year_built >= 2000 && data.year_built <= 2025;
      
      const allGood = bedroomOK && bathroomOK && classificationOK && yearOK;
      
      console.log(`\n🎯 OVERALL STATUS: ${allGood ? '🎉 ALL FIXED!' : '❌ STILL HAS ISSUES'}`);
      
      if (allGood) {
        console.log(`   The property has been successfully re-scraped with correct values!`);
        console.log(`   The database should now contain the updated information.`);
      } else {
        console.log(`   Some issues remain. The extraction logic may need further refinement.`);
      }
      
    } else {
      console.log('❌ No data extracted');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

rescrapeSpecificProperty();
