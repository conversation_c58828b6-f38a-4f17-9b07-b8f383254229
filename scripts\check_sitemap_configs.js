// Check and fix sitemap configurations
require('dotenv').config();
const postgres = require('postgres');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

async function checkAndFixConfigs() {
  console.log('🔍 Checking sitemap configurations...\n');
  
  try {
    // Check current configurations
    const configs = await client`
      SELECT website_id, base_url, sitemap_enabled, sitemap_urls, property_url_patterns
      FROM website_configs 
      WHERE sitemap_enabled = true
      ORDER BY website_id
    `;
    
    console.log('📋 Current sitemap configurations:');
    configs.forEach(config => {
      console.log(`\n🌐 ${config.website_id}:`);
      console.log(`   Base URL: ${config.base_url}`);
      console.log(`   Sitemap Enabled: ${config.sitemap_enabled}`);
      console.log(`   Sitemap URLs: ${config.sitemap_urls}`);
      console.log(`   Property Patterns: ${config.property_url_patterns}`);
    });
    
    // The issue is that the SmartCrawler is not detecting sitemap_enabled properly
    // Let's check if the sitemap_urls field is properly populated
    console.log('\n🔧 Checking sitemap_urls field...');
    
    for (const config of configs) {
      const sitemapUrls = config.sitemap_urls;
      console.log(`\n${config.website_id}: sitemap_urls = "${sitemapUrls}"`);
      
      if (!sitemapUrls || sitemapUrls.trim() === '') {
        console.log(`❌ Empty sitemap_urls for ${config.website_id}`);
      } else {
        try {
          const parsed = JSON.parse(sitemapUrls);
          console.log(`✅ Valid JSON with ${parsed.length} URLs`);
        } catch (error) {
          console.log(`❌ Invalid JSON: ${error.message}`);
        }
      }
    }
    
    // Let's also check the actual database schema
    console.log('\n📊 Checking database schema...');
    const schemaInfo = await client`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'website_configs' 
        AND column_name IN ('sitemap_enabled', 'sitemap_urls', 'sitemap_filters')
      ORDER BY column_name
    `;
    
    console.table(schemaInfo);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

checkAndFixConfigs();
