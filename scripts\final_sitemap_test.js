// Final test of the complete sitemap system
require('dotenv').config();
const { SmartCrawler } = require('../scrape_worker/smart_crawler');
const postgres = require('postgres');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

async function finalSitemapTest() {
  console.log('🎯 Final Sitemap System Test\n');
  
  try {
    const crawler = new SmartCrawler();
    
    // Test all sitemap-enabled websites
    console.log('🌐 Testing all sitemap-enabled websites...\n');
    
    const results = await crawler.checkAllWebsites();
    
    console.log('\n📊 Final Results Summary:');
    console.log(`   Total websites: ${results.total}`);
    console.log(`   Successful: ${results.successful}`);
    console.log(`   Failed: ${results.failed}`);
    console.log(`   Processed: ${results.processed}`);
    
    // Show detailed results
    console.log('\n📋 Detailed Results:');
    results.results.forEach(result => {
      console.log(`\n🌐 ${result.websiteId}:`);
      if (result.success) {
        if (result.result.skipped) {
          console.log(`   ⏭️  Skipped: ${result.result.reason}`);
        } else if (result.result.type === 'sitemap') {
          console.log(`   ✅ Sitemap discovery completed`);
          console.log(`   📊 Total discovered: ${result.result.totalDiscovered}`);
          console.log(`   ➕ New URLs: ${result.result.newUrls}`);
          console.log(`   🗺️  Sitemaps processed: ${result.result.sitemapUrls}`);
        } else if (result.result.type === 'crawl') {
          console.log(`   🕷️  Traditional crawl started`);
          console.log(`   📝 Job ID: ${result.result.jobId}`);
        }
      } else {
        console.log(`   ❌ Failed: ${result.error}`);
      }
    });
    
    // Check database statistics
    console.log('\n📈 Database Statistics:');
    await showDatabaseStats();
    
    console.log('\n🎉 Final test completed successfully!');
    
  } catch (error) {
    console.error('❌ Final test failed:', error.message);
    console.error(error.stack);
  } finally {
    await client.end();
  }
}

async function showDatabaseStats() {
  try {
    // URL discovery stats
    const urlStats = await client`
      SELECT 
        website_id,
        COUNT(*) as total_urls,
        COUNT(*) FILTER (WHERE is_property_page = true) as property_urls,
        COUNT(*) FILTER (WHERE sitemap_url IS NOT NULL) as from_sitemap,
        MAX(discovered_at) as last_discovered
      FROM discovered_urls 
      WHERE website_id IN ('bali_home_immo', 'bali_villa_realty', 'betterplace')
      GROUP BY website_id
      ORDER BY website_id
    `;
    
    if (urlStats.length > 0) {
      console.log('\n📊 URL Discovery Statistics:');
      console.table(urlStats.map(stat => ({
        'Website': stat.website_id,
        'Total URLs': stat.total_urls,
        'Property URLs': stat.property_urls,
        'From Sitemap': stat.from_sitemap,
        'Last Discovered': stat.last_discovered ? new Date(stat.last_discovered).toLocaleString() : 'Never'
      })));
    }
    
    // Scraping queue stats
    const queueStats = await client`
      SELECT 
        website_id,
        COUNT(*) as total_queued,
        COUNT(*) FILTER (WHERE status = 'pending') as pending,
        COUNT(*) FILTER (WHERE status = 'processing') as processing,
        COUNT(*) FILTER (WHERE status = 'completed') as completed
      FROM scraping_queue 
      WHERE website_id IN ('bali_home_immo', 'bali_villa_realty', 'betterplace')
      GROUP BY website_id
      ORDER BY website_id
    `;
    
    if (queueStats.length > 0) {
      console.log('\n📋 Scraping Queue Statistics:');
      console.table(queueStats.map(stat => ({
        'Website': stat.website_id,
        'Total Queued': stat.total_queued,
        'Pending': stat.pending,
        'Processing': stat.processing,
        'Completed': stat.completed
      })));
    }
    
    // Sitemap configuration status
    const configs = await client`
      SELECT 
        website_id,
        name,
        sitemap_enabled,
        last_sitemap_check,
        sitemap_check_frequency_hours
      FROM website_configs 
      WHERE sitemap_enabled = true
      ORDER BY website_id
    `;
    
    if (configs.length > 0) {
      console.log('\n⚙️  Sitemap Configuration Status:');
      console.table(configs.map(config => ({
        'Website ID': config.website_id,
        'Name': config.name,
        'Sitemap Enabled': config.sitemap_enabled ? '✅' : '❌',
        'Last Check': config.last_sitemap_check ? new Date(config.last_sitemap_check).toLocaleString() : 'Never',
        'Check Frequency (hrs)': config.sitemap_check_frequency_hours
      })));
    }
    
  } catch (error) {
    console.error('❌ Error getting database stats:', error.message);
  }
}

finalSitemapTest();
