// Debug Villa Bali Sale property extraction to see raw data
require('dotenv').config();
const { getKeyManager } = require('../scrape_worker/key_manager');

const keyManager = getKeyManager();

async function debugVillaBaliSaleExtraction() {
  console.log('🔍 Debugging Villa Bali Sale Property Extraction\n');
  
  // Test one property URL
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/amed/ocean-views-three-bedroom-freehold-villa-in-amed-vl3341';
  
  console.log(`🏠 Testing URL: ${testUrl}\n`);
  
  try {
    const currentKey = keyManager.getCurrentKey();
    console.log(`🔑 Using key: ${currentKey.maskedKey}`);
    
    // Try different approaches to get content
    console.log('🔄 Trying approach 1: Standard scrape...');

    let data = null;

    // Approach 1: Standard scrape
    try {
      const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentKey.key}`
        },
        body: JSON.stringify({
          url: testUrl,
          formats: ['markdown', 'html'],
          onlyMainContent: false,
          timeout: 60000,
          waitFor: 15000, // Wait even longer for JS
          removeBase64Images: true,
          blockAds: false, // Don't block ads in case it affects content
          proxy: 'auto'
        })
      });

      if (response.ok) {
        data = await response.json();
        console.log('✅ Standard scrape successful');
      } else {
        console.log(`❌ Standard scrape failed: HTTP ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Standard scrape error: ${error.message}`);
    }

    // Approach 2: If no content, try with different settings
    if (!data || (!data.markdown && !data.html)) {
      console.log('🔄 Trying approach 2: Alternative settings...');

      try {
        const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${currentKey.key}`
          },
          body: JSON.stringify({
            url: testUrl,
            formats: ['html'],
            onlyMainContent: false,
            timeout: 90000,
            waitFor: 20000,
            removeBase64Images: false,
            blockAds: false,
            proxy: 'residential'
          })
        });

        if (response.ok) {
          data = await response.json();
          console.log('✅ Alternative scrape successful');
        } else {
          console.log(`❌ Alternative scrape failed: HTTP ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Alternative scrape error: ${error.message}`);
      }
    }

    if (!data) {
      console.log('❌ All scraping approaches failed');
      return;
    }
    

    
    console.log('📄 Raw Extraction Results:');
    console.log('='.repeat(60));
    
    if (data.json) {
      console.log('📊 JSON Data:');
      console.log(JSON.stringify(data.json, null, 2));
    } else {
      console.log('❌ No JSON data returned');
    }

    if (data.markdown) {
      console.log('\n📝 Markdown Content (first 2000 chars):');
      console.log(data.markdown.substring(0, 2000));
      console.log(data.markdown.length > 2000 ? '\n... (truncated)' : '');
    } else {
      console.log('❌ No markdown data returned');
    }

    if (data.html) {
      console.log('\n🌐 HTML Content (first 1000 chars):');
      console.log(data.html.substring(0, 1000));
      console.log(data.html.length > 1000 ? '\n... (truncated)' : '');
    } else {
      console.log('❌ No HTML data returned');
    }
    
    // Test the mapper with this raw data
    console.log('\n🧪 Testing Mapper:');
    console.log('='.repeat(60));

    const { mapVillaBaliSale } = require('../scrape_worker/mappers');

    try {
      // Pass the full data object (including markdown/html) to the mapper
      const mappedProperty = await mapVillaBaliSale({
        url: testUrl,
        json: data.json,
        markdown: data.markdown,
        html: data.html
      });

      console.log('✅ Mapped Property:');
      console.log(JSON.stringify(mappedProperty, null, 2));
      
      // Check validation
      const { validateProperty } = require('../scrape_worker/validate');
      const validation = validateProperty(mappedProperty);
      
      console.log('\n🔍 Validation Result:');
      if (validation.ok) {
        console.log('✅ Property validation passed');
      } else {
        console.log('❌ Property validation failed:');
        validation.errors.forEach(error => {
          console.log(`   - ${error}`);
        });
      }
      
    } catch (mapperError) {
      console.error('❌ Mapper failed:', mapperError.message);
      console.error(mapperError.stack);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
}

debugVillaBaliSaleExtraction();
