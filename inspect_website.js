// Inspect website with <PERSON><PERSON> to see actual content
const { chromium } = require('playwright');

async function inspectWebsite(url) {
  console.log(`🔍 Inspecting website: ${url}`);
  console.log('='.repeat(80));

  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();

  try {
    // Navigate to the page
    console.log('📡 Loading page...');
    await page.goto(url, { waitUntil: 'networkidle' });
    
    // Get page title
    const title = await page.title();
    console.log(`📋 Page Title: ${title}`);
    
    // Look for price information
    console.log('\n💰 PRICE INFORMATION:');
    console.log('-'.repeat(40));
    
    // Try different price selectors
    const priceSelectors = [
      'text=/price/i',
      'text=/\\$/i', 
      'text=/USD/i',
      'text=/IDR/i',
      'text=/Rp/i',
      '[class*="price"]',
      '[id*="price"]',
      'text=/million/i',
      'text=/rental/i',
      'text=/month/i'
    ];
    
    for (const selector of priceSelectors) {
      try {
        const elements = await page.locator(selector).all();
        if (elements.length > 0) {
          console.log(`\n🎯 Found with selector "${selector}":`);
          for (let i = 0; i < Math.min(elements.length, 3); i++) {
            const text = await elements[i].textContent();
            if (text && text.trim()) {
              console.log(`   ${i + 1}. "${text.trim()}"`);
            }
          }
        }
      } catch (error) {
        // Selector not found, continue
      }
    }
    
    // Look for property details
    console.log('\n🏠 PROPERTY DETAILS:');
    console.log('-'.repeat(40));
    
    const detailSelectors = [
      'text=/bedroom/i',
      'text=/bathroom/i',
      'text=/bed/i',
      'text=/bath/i',
      'text=/sqm/i',
      'text=/sqft/i',
      'text=/freehold/i',
      'text=/leasehold/i'
    ];
    
    for (const selector of detailSelectors) {
      try {
        const elements = await page.locator(selector).all();
        if (elements.length > 0) {
          console.log(`\n🎯 Found with selector "${selector}":`);
          for (let i = 0; i < Math.min(elements.length, 2); i++) {
            const text = await elements[i].textContent();
            if (text && text.trim()) {
              console.log(`   ${i + 1}. "${text.trim()}"`);
            }
          }
        }
      } catch (error) {
        // Selector not found, continue
      }
    }
    
    // Get all text content to analyze
    console.log('\n📄 FULL PAGE TEXT (first 2000 chars):');
    console.log('-'.repeat(40));
    const fullText = await page.textContent('body');
    console.log(fullText.substring(0, 2000) + '...');
    
    // Look for specific patterns in the text
    console.log('\n🔍 PATTERN ANALYSIS:');
    console.log('-'.repeat(40));
    
    const patterns = [
      /(\$[\d,\.]+)/gi,
      /(USD[\s]*[\d,\.]+)/gi,
      /(IDR[\s]*[\d,\.]+)/gi,
      /(Rp[\s]*[\d,\.]+)/gi,
      /([\d,\.]+[\s]*million)/gi,
      /([\d,\.]+[\s]*billion)/gi,
      /(\d+[\s]*bedroom)/gi,
      /(\d+[\s]*bathroom)/gi,
      /(freehold|leasehold)/gi,
      /(monthly|yearly|per month|per year)/gi
    ];
    
    patterns.forEach((pattern, index) => {
      const matches = fullText.match(pattern);
      if (matches) {
        console.log(`Pattern ${index + 1} (${pattern.source}): ${matches.slice(0, 3).join(', ')}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error inspecting website:', error.message);
  } finally {
    await browser.close();
  }
}

// Run if called directly
if (require.main === module) {
  const url = process.argv[2] || 'https://balicoconutliving.com/bali-villa-monthly-rental/Jimbaran/3629-V013-3416/Bee-Balm-Villa';
  
  inspectWebsite(url)
    .then(() => {
      console.log('\n✅ Website inspection completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Inspection failed:', error.message);
      process.exit(1);
    });
}

module.exports = { inspectWebsite };
