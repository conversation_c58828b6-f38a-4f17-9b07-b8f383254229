require('dotenv').config();
const { Pool } = require('pg');

async function checkDatabaseResults() {
  console.log('🔍 CHECKING DATABASE RESULTS');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get recently scraped properties
    const result = await pool.query(`
      SELECT 
        id,
        external_id,
        title,
        bedrooms,
        bathrooms,
        category,
        type,
        year_built,
        created_at,
        source_url
      FROM property 
      ORDER BY created_at DESC
      LIMIT 20
    `);
    
    console.log(`📊 Found ${result.rows.length} properties in database`);
    console.log('\n📋 RECENT PROPERTIES:');
    console.log('='.repeat(120));
    
    result.rows.forEach((prop, index) => {
      const bedroomStatus = prop.bedrooms ? '✅' : '❌';
      const bathroomStatus = prop.bathrooms ? '✅' : '❌';
      const yearStatus = prop.year_built ? '✅' : '❌';
      
      console.log(`${index + 1}. ${prop.external_id} - ${prop.title.substring(0, 50)}...`);
      console.log(`   Bedrooms: ${prop.bedrooms} ${bedroomStatus} | Bathrooms: ${prop.bathrooms} ${bathroomStatus} | Year: ${prop.year_built} ${yearStatus}`);
      console.log(`   Category: ${prop.category} | Type: ${prop.type}`);
      console.log(`   URL: ${prop.source_url}`);
      console.log('');
    });
    
    // Statistics
    const stats = await pool.query(`
      SELECT 
        COUNT(*) as total,
        COUNT(bedrooms) as has_bedrooms,
        COUNT(bathrooms) as has_bathrooms,
        COUNT(year_built) as has_year_built,
        AVG(bedrooms) as avg_bedrooms,
        AVG(bathrooms) as avg_bathrooms
      FROM property
    `);
    
    const stat = stats.rows[0];
    console.log('📊 STATISTICS:');
    console.log('='.repeat(40));
    console.log(`Total properties: ${stat.total}`);
    console.log(`Has bedrooms: ${stat.has_bedrooms}/${stat.total} (${((stat.has_bedrooms/stat.total)*100).toFixed(1)}%)`);
    console.log(`Has bathrooms: ${stat.has_bathrooms}/${stat.total} (${((stat.has_bathrooms/stat.total)*100).toFixed(1)}%)`);
    console.log(`Has year built: ${stat.has_year_built}/${stat.total} (${((stat.has_year_built/stat.total)*100).toFixed(1)}%)`);
    console.log(`Average bedrooms: ${parseFloat(stat.avg_bedrooms || 0).toFixed(1)}`);
    console.log(`Average bathrooms: ${parseFloat(stat.avg_bathrooms || 0).toFixed(1)}`);
    
    // Check for problematic patterns
    const problems = await pool.query(`
      SELECT 
        external_id,
        title,
        bedrooms,
        bathrooms,
        source_url
      FROM property 
      WHERE 
        (title ILIKE '%bedroom%' AND bedrooms IS NULL) OR
        (bathrooms > 10) OR
        (bedrooms > 10)
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    if (problems.rows.length > 0) {
      console.log('\n❌ PROBLEMATIC PROPERTIES:');
      console.log('='.repeat(60));
      problems.rows.forEach((prop, index) => {
        console.log(`${index + 1}. ${prop.external_id} - ${prop.title.substring(0, 40)}...`);
        console.log(`   Bedrooms: ${prop.bedrooms} | Bathrooms: ${prop.bathrooms}`);
        console.log(`   URL: ${prop.source_url}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkDatabaseResults();
