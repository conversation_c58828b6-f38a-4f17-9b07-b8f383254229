// Enhanced Scraper with Smart Retries and Fallbacks
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, scrapingQueue, properties } = require('./drizzle_client');
const { eq, and, lt } = require('drizzle-orm');

class EnhancedScraper {
  constructor() {
    this.queueManager = new QueueManager();
    this.retryStrategies = {
      'rate_limit': { delay: 60000, maxRetries: 5 }, // 1 minute
      'no_data': { delay: 300000, maxRetries: 2 }, // 5 minutes  
      'validation': { delay: 120000, maxRetries: 3 }, // 2 minutes
      'credit_limit': { delay: 3600000, maxRetries: 1 }, // 1 hour
      'unknown': { delay: 180000, maxRetries: 2 } // 3 minutes
    };
  }

  // Classify error type for smart retry
  classifyError(errorMessage) {
    if (!errorMessage) return 'unknown';
    
    const msg = errorMessage.toLowerCase();
    
    if (msg.includes('rate limit') || msg.includes('429')) return 'rate_limit';
    if (msg.includes('no data') || msg.includes('0 results')) return 'no_data';
    if (msg.includes('validation failed')) return 'validation';
    if (msg.includes('insufficient credits') || msg.includes('402')) return 'credit_limit';
    
    return 'unknown';
  }

  // Smart retry with exponential backoff
  async smartRetry(websiteId, maxItems = 10) {
    console.log(`🔄 Starting smart retry for ${websiteId}...`);
    
    // Get failed items that are ready for retry
    const now = new Date();
    const failedItems = await db.select()
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, websiteId),
        eq(scrapingQueue.status, 'failed')
      ))
      .limit(maxItems);

    if (failedItems.length === 0) {
      console.log(`ℹ️  No failed items ready for retry for ${websiteId}`);
      return { processed: 0, success: 0, failed: 0 };
    }

    console.log(`🔄 Found ${failedItems.length} items ready for retry`);
    
    let processed = 0;
    let success = 0;
    let failed = 0;

    for (const item of failedItems) {
      const errorType = this.classifyError(item.error_message);
      const strategy = this.retryStrategies[errorType];
      
      console.log(`\n🔄 Retrying: ${item.url.substring(0, 60)}...`);
      console.log(`   Error type: ${errorType}, Attempt: ${item.attempts + 1}/${strategy.maxRetries + 1}`);
      
      if (item.attempts >= strategy.maxRetries) {
        console.log(`❌ Max retries exceeded, marking as permanently failed`);
        await db.update(scrapingQueue)
          .set({ 
            status: 'permanently_failed',
            updated_at: new Date()
          })
          .where(eq(scrapingQueue.id, item.id));
        failed++;
        continue;
      }

      // Reset to pending for retry
      await db.update(scrapingQueue)
        .set({ 
          status: 'pending',
          attempts: item.attempts + 1,
          error_message: null,
          retry_after: null,
          updated_at: new Date()
        })
        .where(eq(scrapingQueue.id, item.id));

      processed++;
      
      // Process immediately with enhanced error handling
      try {
        await this.queueManager.processQueue(websiteId, 1);
        success++;
        console.log(`✅ Retry successful`);
      } catch (error) {
        console.log(`❌ Retry failed: ${error.message}`);
        failed++;
      }

      // Wait between retries to avoid rate limits
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    console.log(`\n📊 Smart retry results for ${websiteId}:`);
    console.log(`   Processed: ${processed}, Success: ${success}, Failed: ${failed}`);
    
    return { processed, success, failed };
  }

  // Generate better URLs based on successful patterns
  async generateBetterUrls(websiteId, count = 20) {
    console.log(`🎯 Generating better URLs for ${websiteId}...`);
    
    // Get successful URLs to learn patterns
    const successfulProps = await db.select()
      .from(properties)
      .where(eq(properties.source_id, websiteId))
      .limit(10);

    const newUrls = [];

    if (websiteId === 'betterplace') {
      // Use successful property patterns
      const baseIds = [2400, 2450, 2500, 2550, 2600, 2650, 2700, 2750, 2800, 2850];
      
      for (let i = 0; i < count; i++) {
        const baseId = baseIds[i % baseIds.length];
        const propertyId = baseId + Math.floor(i / baseIds.length);
        const url = `https://betterplace.cc/buy/properties/BPVL${propertyId.toString().padStart(5, '0')}`;
        
        newUrls.push({
          website_id: websiteId,
          url: url,
          priority: 8,
          source: 'pattern_improved'
        });
      }
    }

    if (websiteId === 'bali_home_immo') {
      // Use actual working URL patterns from successful scrapes
      const workingPatterns = [
        'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/',
        'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/',
        'https://bali-home-immo.com/realestate-property/for-rent/other/monthly/'
      ];
      
      const realLocations = [
        'canggu', 'seminyak', 'ubud', 'jimbaran', 'uluwatu', 'kerobokan',
        'sanur', 'denpasar', 'nusa_dua', 'berawa', 'petitenget', 'bingin'
      ];

      for (let i = 0; i < count; i++) {
        const pattern = workingPatterns[i % workingPatterns.length];
        const location = realLocations[i % realLocations.length];
        const url = pattern + location;
        
        newUrls.push({
          website_id: websiteId,
          url: url,
          priority: 8,
          source: 'location_based'
        });
      }
    }

    // Add URLs to queue
    let added = 0;
    for (const urlData of newUrls) {
      try {
        await db.insert(scrapingQueue).values({
          website_id: urlData.website_id,
          url: urlData.url,
          priority: urlData.priority,
          status: 'pending',
          attempts: 0,
          created_at: new Date(),
          updated_at: new Date()
        });
        added++;
      } catch (error) {
        if (!error.message.includes('duplicate')) {
          console.log(`❌ Error adding URL: ${error.message}`);
        }
      }
    }

    console.log(`✅ Added ${added} better URLs for ${websiteId}`);
    return added;
  }

  // Enhanced processing with better error handling
  async enhancedProcess(websiteId, maxItems = 10) {
    console.log(`🚀 Enhanced processing for ${websiteId}...`);
    
    // 1. First try smart retries
    const retryResults = await this.smartRetry(websiteId, 5);
    
    // 2. Generate better URLs if queue is low
    const pendingCount = await db.select()
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, websiteId),
        eq(scrapingQueue.status, 'pending')
      ));

    if (pendingCount.length < 10) {
      console.log(`📋 Queue low (${pendingCount.length}), generating better URLs...`);
      await this.generateBetterUrls(websiteId, 20);
    }

    // 3. Process new items with enhanced settings
    console.log(`\n📦 Processing ${maxItems} new items...`);
    const processResults = await this.queueManager.processQueue(websiteId, maxItems);

    // 4. Get final stats
    const totalProps = await db.select().from(properties);
    const websiteProps = await db.select()
      .from(properties)
      .where(eq(properties.source_id, websiteId));

    console.log(`\n📊 Enhanced processing results:`);
    console.log(`   Retries: ${retryResults.success}/${retryResults.processed}`);
    console.log(`   New items: ${processResults?.processed || 0}`);
    console.log(`   ${websiteId} properties: ${websiteProps.length}`);
    console.log(`   Total properties: ${totalProps.length}`);

    return {
      retries: retryResults,
      newItems: processResults,
      totalProperties: totalProps.length
    };
  }

  // Run enhanced processing for all websites
  async runEnhanced() {
    console.log('🚀 Starting enhanced scraping session...');
    
    const websites = ['betterplace', 'bali_home_immo'];
    const results = {};

    for (const websiteId of websites) {
      console.log(`\n🔍 Processing ${websiteId}...`);
      results[websiteId] = await this.enhancedProcess(websiteId, 8);
      
      // Wait between websites
      console.log('⏳ Waiting 30 seconds before next website...');
      await new Promise(resolve => setTimeout(resolve, 30000));
    }

    const totalProps = await db.select().from(properties);
    console.log(`\n🎯 FINAL RESULT: ${totalProps.length} total properties`);
    
    return results;
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const enhancedScraper = new EnhancedScraper();

  try {
    switch (command) {
      case 'run':
        await enhancedScraper.runEnhanced();
        break;

      case 'retry':
        const websiteId = args[1] || 'betterplace';
        await enhancedScraper.smartRetry(websiteId, 10);
        break;

      case 'generate':
        const website = args[1] || 'betterplace';
        const count = parseInt(args[2]) || 20;
        await enhancedScraper.generateBetterUrls(website, count);
        break;

      default:
        console.log('Enhanced Scraper with Smart Retries');
        console.log('');
        console.log('Commands:');
        console.log('  run                    - Run enhanced processing for all websites');
        console.log('  retry [website]        - Smart retry failed items');
        console.log('  generate [website] [count] - Generate better URLs');
        console.log('');
        console.log('Examples:');
        console.log('  node enhanced_scraper.js run');
        console.log('  node enhanced_scraper.js retry betterplace');
        console.log('  node enhanced_scraper.js generate bali_home_immo 30');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }

  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = { EnhancedScraper };
