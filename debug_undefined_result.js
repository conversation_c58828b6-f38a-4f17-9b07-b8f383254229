require('dotenv').config();
const { mapBetterPlace } = require('./scrape_worker/mappers');

async function debugUndefinedResult() {
  console.log('🔍 DEBUGGING "Scraper returned undefined result"...');
  
  // Test with a simple mock data that should work
  const mockData = {
    markdown: `
      # Test Villa in Canggu
      Price: USD 250,000
      Location: Canggu, Bali
      2 bedrooms, 2 bathrooms
      Building size: 150 sqm
      Land size: 200 sqm
    `,
    url: 'https://betterplace.cc/buy/properties/BPVL00999'
  };
  
  try {
    console.log('📝 Testing mapper with mock data...');
    const result = await mapBetterPlace(mockData);
    
    if (result === undefined) {
      console.log('❌ MAPPER RETURNED UNDEFINED!');
    } else if (result === null) {
      console.log('⚠️  MAPPER RETURNED NULL (property skipped)');
    } else {
      console.log('✅ MAPPER RETURNED VALID RESULT:');
      console.log(`   Title: ${result.title}`);
      console.log(`   Price: ${result.price}`);
      console.log(`   Bedrooms: ${result.bedrooms}`);
      console.log(`   Bathrooms: ${result.bathrooms}`);
    }
    
  } catch (error) {
    console.error('❌ MAPPER ERROR:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

debugUndefinedResult();
