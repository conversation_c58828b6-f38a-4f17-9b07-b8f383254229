require('dotenv').config();

// Test the property classification functions
function detectBetterPlacePropertyType(property_id, title, markdown) {
  const id = property_id?.toUpperCase() || '';
  const text = `${title} ${markdown}`.toLowerCase();

  console.log(`🔍 Testing property type detection:`);
  console.log(`   ID: ${id}`);
  console.log(`   Title: "${title}"`);
  console.log(`   Text preview: "${text.substring(0, 100)}..."`);

  // Content based detection FIRST (more accurate for edge cases)
  if (text.includes('apartment') || text.includes('condo')) {
    console.log(`   ✅ Content-based: Found "apartment" → apartment`);
    return 'apartment';
  }
  if (text.includes('land') || text.includes('plot') || text.includes('lot')) {
    console.log(`   ✅ Content-based: Found land keywords → land`);
    return 'land';
  }
  if (text.includes('hotel') || text.includes('resort')) {
    console.log(`   ✅ Content-based: Found hotel keywords → hotel`);
    return 'hotel';
  }
  if (text.includes('villa') || text.includes('house')) {
    console.log(`   ✅ Content-based: Found villa keywords → villa`);
    return 'villa';
  }

  console.log(`   🔍 No content match, checking ID patterns...`);

  // Property ID based detection as fallback (when content is unclear)
  if (id.startsWith('BPVL')) {
    console.log(`   📋 ID-based: BPVL → villa`);
    return 'villa';
  }
  if (id.startsWith('BPVF')) {
    console.log(`   📋 ID-based: BPVF → villa`);
    return 'villa';
  }
  if (id.startsWith('BPHL')) {
    console.log(`   📋 ID-based: BPHL → hotel`);
    return 'hotel';
  }
  if (id.startsWith('BPAP')) {
    console.log(`   📋 ID-based: BPAP → apartment`);
    return 'apartment';
  }
  if (id.startsWith('BPLD') || id.startsWith('BPLF')) {
    console.log(`   📋 ID-based: BPLD/BPLF → land`);
    return 'land';
  }
  if (id.startsWith('BPLL')) {
    console.log(`   📋 ID-based: BPLL → land`);
    return 'land';
  }
  if (id.startsWith('BPFL')) {
    console.log(`   📋 ID-based: BPFL → land`);
    return 'land';
  }

  console.log(`   🏠 Default fallback → villa`);
  return 'villa';
}

function classifyProperty(title, description) {
  const text = `${title} ${description}`.toLowerCase();
  
  console.log(`🏷️ Testing property classification:`);
  console.log(`   Text: "${text.substring(0, 100)}..."`);

  // Commercial property detection - use more precise patterns to avoid false positives
  const commercialPatterns = [
    /\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent)\b/,
    /\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/,
    /\b(commercial\s+property|commercial\s+building|commercial\s+space)\b/,
    /\b(store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/,
    /\b(warehouse\s+for\s+sale|warehouse\s+for\s+rent|storage\s+facility)\b/,
    /\b(business\s+premises|business\s+property)\b/
  ];

  const isCommercial = commercialPatterns.some(pattern => pattern.test(text));
  if (isCommercial) {
    console.log(`   🏢 Commercial pattern matched → COMMERCIAL`);
    return { category: 'COMMERCIAL', type: 'OTHER' };
  }

  // Residential property detection
  if (text.includes('villa')) {
    console.log(`   🏠 Found "villa" → RESIDENTIAL VILLA`);
    return { category: 'RESIDENTIAL', type: 'VILLA' };
  }
  if (text.includes('apartment') || text.includes('apt')) {
    console.log(`   🏠 Found "apartment" → RESIDENTIAL APARTMENT`);
    return { category: 'RESIDENTIAL', type: 'APARTMENT' };
  }
  if (text.includes('house') || text.includes('home')) {
    console.log(`   🏠 Found "house" → RESIDENTIAL HOUSE`);
    return { category: 'RESIDENTIAL', type: 'HOUSE' };
  }
  if (text.includes('condo') || text.includes('condominium')) {
    console.log(`   🏠 Found "condo" → RESIDENTIAL CONDO`);
    return { category: 'RESIDENTIAL', type: 'CONDO' };
  }
  if (text.includes('townhouse') || text.includes('town house')) {
    console.log(`   🏠 Found "townhouse" → RESIDENTIAL TOWNHOUSE`);
    return { category: 'RESIDENTIAL', type: 'TOWNHOUSE' };
  }
  if (text.includes('loft')) {
    console.log(`   🏠 Found "loft" → RESIDENTIAL LOFT`);
    return { category: 'RESIDENTIAL', type: 'LOFT' };
  }

  // Land detection
  if (text.includes('land') || text.includes('plot') || text.includes('lot') || text.includes('terrain')) {
    console.log(`   🌍 Found land keywords → LAND`);
    return { category: 'LAND', type: 'LAND' };
  }

  // Industrial detection
  if (text.includes('factory') || text.includes('manufacturing') || text.includes('industrial')) {
    console.log(`   🏭 Found industrial keywords → INDUSTRIAL`);
    return { category: 'INDUSTRIAL', type: 'OTHER' };
  }

  console.log(`   🏠 Default fallback → RESIDENTIAL VILLA`);
  return { category: 'RESIDENTIAL', type: 'VILLA' };
}

// Test problematic properties from database
const testCases = [
  {
    id: 'BPLL00727',
    title: 'Seaside Dreams Unleashed: Exclusive Leasehold Land in Pangkung Tibah – Kedungu Awaits Your Vision',
    expected: 'LAND'
  },
  {
    id: 'BPVF00745', 
    title: 'Spacious and Modern Living, 3 Bedroom Brand New Villa in Cepaka',
    expected: 'VILLA'
  },
  {
    id: 'BPHL00746',
    title: 'Live the High Life: Elegant and Furnished Leasehold 1-Bed Panoramic Apartment in Canggu – Lima Beach',
    expected: 'APARTMENT'
  }
];

console.log('🧪 TESTING PROPERTY CLASSIFICATION');
console.log('='.repeat(80));

testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. TESTING: ${testCase.id}`);
  console.log('='.repeat(60));
  
  const propertyType = detectBetterPlacePropertyType(testCase.id, testCase.title, '');
  const classification = classifyProperty(testCase.title, '');
  
  console.log(`\n📊 RESULTS:`);
  console.log(`   Property Type: ${propertyType} (expected: ${testCase.expected.toLowerCase()})`);
  console.log(`   Classification: ${classification.category} ${classification.type}`);
  
  const typeCorrect = propertyType.toLowerCase() === testCase.expected.toLowerCase();
  const classCorrect = classification.type.toUpperCase() === testCase.expected.toUpperCase();
  
  console.log(`   Property Type: ${typeCorrect ? '✅' : '❌'}`);
  console.log(`   Classification: ${classCorrect ? '✅' : '❌'}`);
});
