// Quick test of SOLD filtering
require('dotenv').config();

async function quickSoldTest() {
  console.log('🚫 Quick SOLD Test\n');
  
  try {
    const { mapBaliVillaRealty } = require('../scrape_worker/mappers');
    
    const soldProperty = {
      markdown: `# Villa (SOLD)\n**Price:** $100,000\n**Location:** Bali`,
      url: 'https://test.com'
    };
    
    console.log('Testing SOLD property...');
    const result = await mapBaliVillaRealty(soldProperty);
    
    if (result === null) {
      console.log('✅ SOLD property correctly filtered out');
    } else {
      console.log('❌ SOLD property NOT filtered out');
      console.log(`Status: ${result.status}`);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

quickSoldTest();
