// Debug mapper location issue
require('dotenv').config();

async function debugMapperLocation() {
  const testMarkdown = `Share![share icon](https://betterplace.cc/_next/static/media/share-thin-black.f25c361e.svg)![Freehold Land with Building in Prime Pecatu Location – Pink Tourism Zone](https://betterplace.cc/_next/image?url=https%3A%2F%2Fbetterplace.sgp1.cdn.digitaloceanspaces.com%2FCACHE%2Fimages%2Fprocessed%2F3f2c482f-3686-4da6-86c1-779eb8e0f35c_717986a05e9421d204825107c52223f8f6d6770b11a215147%2Ff8a3ccfcbf3e105012726ecad6667b68.webp&w=3840&q=60)

# Freehold Land with Building in Prime Pecatu Location – Pink Tourism Zone

- BPLF02065Property ID
- LandProperty type  
- Bukit - Pecatu
- FreeholdOwnership type
- 1035 sqmLand size
- 15 MinutesTo the beach
- LandFurnishing

**Price : IDR 650,000,000/ Are**`;

  console.log('🔍 Testing location extraction patterns:');
  
  // Test all patterns individually
  const patterns = [
    {
      name: 'Cities pattern',
      regex: /(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi|Tabanan|Badung|Gianyar)/i
    },
    {
      name: 'Prime Location pattern',
      regex: /Prime\s+([A-Za-z\s]+)\s+Location/i
    },
    {
      name: 'In Location pattern', 
      regex: /in\s+([A-Za-z\s]+)\s+Location/i
    },
    {
      name: 'Bukit dash pattern',
      regex: /Bukit\s*-\s*([A-Za-z\s]+)/i
    },
    {
      name: 'Location colon pattern',
      regex: /Location[:\s]*([^\n]+)/i
    },
    {
      name: 'Address colon pattern',
      regex: /Address[:\s]*([^\n]+)/i
    }
  ];
  
  patterns.forEach((pattern, index) => {
    const match = testMarkdown.match(pattern.regex);
    console.log(`${index + 1}. ${pattern.name}:`);
    console.log(`   Regex: ${pattern.regex}`);
    console.log(`   Match: ${match ? JSON.stringify(match) : 'NO MATCH'}`);
    console.log(`   Result: ${match ? (match[1]?.trim() || match[0]?.trim()) : 'undefined'}`);
    console.log('');
  });
  
  // Test the actual mapper logic
  console.log('🔄 Testing actual mapper logic:');
  const locationMatch = testMarkdown.match(/(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi|Tabanan|Badung|Gianyar)/i) ||
                       testMarkdown.match(/Prime\s+([A-Za-z\s]+)\s+Location/i) ||
                       testMarkdown.match(/in\s+([A-Za-z\s]+)\s+Location/i) ||
                       testMarkdown.match(/Bukit\s*-\s*([A-Za-z\s]+)/i) ||
                       testMarkdown.match(/Location[:\s]*([^\n]+)/i) ||
                       testMarkdown.match(/Address[:\s]*([^\n]+)/i);
  const location = locationMatch ? locationMatch[1]?.trim() || locationMatch[0]?.trim() : 'Bali, Indonesia';
  
  console.log(`Final locationMatch: ${JSON.stringify(locationMatch)}`);
  console.log(`Final location: ${location}`);
  
  // Test the full mapper
  console.log('\n🔄 Testing full mapper:');
  const { mapBetterPlace } = require('./scrape_worker/mappers');
  
  const result = await mapBetterPlace({
    markdown: testMarkdown,
    url: 'https://betterplace.cc/buy/properties/BPLF02065'
  });
  
  console.log('Mapper result:', {
    title: result?.title,
    location: result?.location,
    property_type: result?.property_type,
    price: result?.price,
    bedrooms: result?.bedrooms,
    bathrooms: result?.bathrooms
  });
}

debugMapperLocation();
