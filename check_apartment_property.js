require('dotenv').config();
const { Pool } = require('pg');

async function checkApartmentProperty() {
  console.log('🔍 CHECKING APARTMENT PROPERTY CLASSIFICATION');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get the specific apartment property
    const result = await pool.query(`
      SELECT 
        id,
        external_id,
        title,
        category,
        type,
        created_at,
        last_scraped_at,
        source_url
      FROM property 
      WHERE id = '4bbb8b0e-ceb2-43c5-b164-4302631c754a'
         OR title LIKE '%Suite Apartments in Tumbak Bayuh%'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ Property not found');
      return;
    }
    
    const property = result.rows[0];
    
    console.log(`📊 PROPERTY DETAILS:`);
    console.log(`   ID: ${property.id}`);
    console.log(`   External ID: ${property.external_id}`);
    console.log(`   Title: ${property.title}`);
    console.log(`   Category: ${property.category} ${property.category === 'COMMERCIAL' ? '❌' : '✅'}`);
    console.log(`   Type: ${property.type}`);
    console.log(`   Created: ${property.created_at}`);
    console.log(`   Last Scraped: ${property.last_scraped_at}`);
    console.log(`   URL: ${property.source_url}`);
    
    // Check if this was scraped recently (after our fix)
    const scrapedRecently = property.last_scraped_at && new Date(property.last_scraped_at) > new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    console.log(`\n📅 TIMING ANALYSIS:`);
    console.log(`   Scraped in last 24h: ${scrapedRecently ? '✅ Yes' : '❌ No'}`);
    
    if (!scrapedRecently) {
      console.log(`\n🎯 LIKELY CAUSE: This property was scraped BEFORE the classification fix`);
      console.log(`   The property needs to be re-scraped to get the correct RESIDENTIAL classification`);
    } else {
      console.log(`\n❌ PROBLEM: This was scraped recently but still has wrong classification`);
      console.log(`   Need to investigate why the classification fix isn't working`);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkApartmentProperty();
