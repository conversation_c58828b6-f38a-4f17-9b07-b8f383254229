require('dotenv').config();
const { mapBetterPlace } = require('./scrape_worker/mappers');

async function testSpecificURLs() {
  console.log('🔍 TESTING SPECIFIC URLs FOR BEDROOM/BATHROOM EXTRACTION');
  console.log('='.repeat(70));
  
  const testUrls = [
    {
      id: 'BPVL00901',
      url: 'https://betterplace.cc/buy/properties/BPVL00901',
      dbBedrooms: 2,
      dbBathrooms: 2,
      title: 'Modern 2 Bedroom Villa in Ubud'
    },
    {
      id: 'BPVL00912', 
      url: 'https://betterplace.cc/buy/properties/BPVL00912',
      dbBedrooms: 1,
      dbBathrooms: 1,
      title: 'Brand New and Charming 1 Bedroom Villa'
    },
    {
      id: 'BPVL00910',
      url: 'https://betterplace.cc/buy/properties/BPVL00910', 
      dbBedrooms: 3,
      dbBathrooms: 2,
      title: 'Exquisite 3 Bedroom Villa in Bumbak'
    }
  ];
  
  for (const testCase of testUrls) {
    console.log(`\n🏠 ANALYZING ${testCase.id}:`);
    console.log(`📍 URL: ${testCase.url}`);
    console.log(`💾 Database: ${testCase.dbBedrooms} bed / ${testCase.dbBathrooms} bath`);
    console.log(`📝 Title: ${testCase.title}`);
    
    try {
      console.log('📥 Testing with current mapper...');

      // Create mock data to test the mapper
      const mockData = {
        markdown: `Test data for ${testCase.id}`,
        url: testCase.url
      };

      // Test the current mapper
      const mapperResult = await mapBetterPlace(mockData);

      console.log('🔧 MAPPER TEST RESULT:');
      if (mapperResult) {
        console.log(`   Mapper bedrooms: ${mapperResult.bedrooms}`);
        console.log(`   Mapper bathrooms: ${mapperResult.bathrooms}`);
        console.log(`   Database bedrooms: ${testCase.dbBedrooms}`);
        console.log(`   Database bathrooms: ${testCase.dbBathrooms}`);
        console.log(`   Match: ${mapperResult.bedrooms === testCase.dbBedrooms && mapperResult.bathrooms === testCase.dbBathrooms ? '✅' : '❌'}`);
      } else {
        console.log('   ❌ Mapper returned null/undefined');
      }

      // For now, skip the detailed HTML analysis
      const markdown = 'Mock markdown for testing';
      
      if (markdown) {
        console.log(`📄 Got ${markdown.length} characters of markdown`);
        
        // Test the CSS patterns
        console.log('\n🔍 TESTING CSS PATTERNS:');
        
        // Current CSS patterns (the ones we "fixed")
        const bedroomCssPattern = /bedrooms\.7a6788f7\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
        const bathroomCssPattern = /bathrooms\.45d31171\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
        
        const bedroomMatch = markdown.match(bedroomCssPattern);
        const bathroomMatch = markdown.match(bathroomCssPattern);
        
        console.log(`   Bedroom CSS: ${bedroomMatch ? `Found "${bedroomMatch[1]}"` : 'NOT FOUND'}`);
        console.log(`   Bathroom CSS: ${bathroomMatch ? `Found "${bathroomMatch[1]}"` : 'NOT FOUND'}`);
        
        // Test alternative patterns
        console.log('\n🔍 TESTING ALTERNATIVE PATTERNS:');
        
        // Look for any bedroom/bathroom mentions
        const bedroomMentions = markdown.match(/(\d+)\s*bedroom/gi);
        const bathroomMentions = markdown.match(/(\d+)\s*bathroom/gi);
        
        console.log(`   Bedroom mentions: ${bedroomMentions ? bedroomMentions.join(', ') : 'None'}`);
        console.log(`   Bathroom mentions: ${bathroomMentions ? bathroomMentions.join(', ') : 'None'}`);
        
        // Look for Details section
        const detailsIndex = markdown.indexOf('Details');
        if (detailsIndex !== -1) {
          const detailsSection = markdown.substring(detailsIndex, detailsIndex + 1000);
          console.log('\n📋 DETAILS SECTION (first 1000 chars):');
          console.log(detailsSection);
        } else {
          console.log('\n❌ No "Details" section found');
        }
        
        // Look for any numbers that could be bedrooms/bathrooms
        const numberPattern = /(\d+)/g;
        const numbers = markdown.match(numberPattern);
        if (numbers) {
          const uniqueNumbers = [...new Set(numbers)].sort((a, b) => parseInt(a) - parseInt(b));
          console.log(`\n🔢 All numbers found: ${uniqueNumbers.slice(0, 20).join(', ')}${uniqueNumbers.length > 20 ? '...' : ''}`);
        }
        
      } else {
        console.log('❌ Failed to get markdown');
      }
      
    } catch (error) {
      console.error(`❌ Error: ${error.message}`);
    }
    
    console.log('\n' + '-'.repeat(50));
  }
}

testSpecificURLs();
