require('dotenv').config();
const { Pool } = require('pg');
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testHostelProperty() {
  console.log('🔍 TESTING SUSPICIOUS PROPERTY: BPHL00943 (15 bed / 7 bath)');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get the hostel property from database
    const result = await pool.query(`
      SELECT 
        id,
        external_id,
        title,
        bedrooms,
        bathrooms,
        source_url,
        created_at
      FROM property 
      WHERE external_id = 'BPHL00943'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ BPHL00943 not found in database');
      return;
    }
    
    const property = result.rows[0];
    
    console.log(`🏨 DATABASE VALUES (HOSTEL):`);
    console.log(`   ID: ${property.id}`);
    console.log(`   External ID: ${property.external_id}`);
    console.log(`   URL: ${property.source_url}`);
    console.log(`   Database: ${property.bedrooms} bed / ${property.bathrooms} bath`);
    console.log(`   Title: ${property.title}`);
    console.log(`   Created: ${property.created_at}`);
    
    console.log('\n🚨 ANALYSIS: 15 bedrooms seems very high for a normal property!');
    console.log('This could be:');
    console.log('1. A hostel/hotel with many rooms');
    console.log('2. An extraction error (wrong number picked up)');
    console.log('3. A legitimate large property');
    
    console.log('\n🔄 RE-SCRAPING WITH CURRENT MAPPER...');
    
    try {
      // Use the actual scraper to get fresh data
      const scrapingResult = await runExtractBatch('betterplace', [property.source_url], {});
      
      if (scrapingResult && scrapingResult.extractedData && scrapingResult.extractedData.length > 0) {
        const freshData = scrapingResult.extractedData[0];
        
        console.log(`\n🆕 FRESH SCRAPE RESULTS:`);
        console.log(`   Bedrooms: ${freshData.bedrooms}`);
        console.log(`   Bathrooms: ${freshData.bathrooms}`);
        console.log(`   Title: ${freshData.title}`);
        console.log(`   Price: ${freshData.price}`);
        
        // Compare
        const bedroomMatch = freshData.bedrooms === property.bedrooms;
        const bathroomMatch = freshData.bathrooms === property.bathrooms;
        
        console.log(`\n📊 DETAILED COMPARISON:`);
        console.log(`   Bedrooms: ${property.bedrooms} (DB) vs ${freshData.bedrooms} (Fresh) ${bedroomMatch ? '✅' : '❌'}`);
        console.log(`   Bathrooms: ${property.bathrooms} (DB) vs ${freshData.bathrooms} (Fresh) ${bathroomMatch ? '✅' : '❌'}`);
        
        if (!bedroomMatch || !bathroomMatch) {
          console.log(`\n❌ MISMATCH DETECTED!`);
          console.log(`💾 DATABASE: ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms`);
          console.log(`🆕 FRESH: ${freshData.bedrooms} bedrooms, ${freshData.bathrooms} bathrooms`);
          
          // Analyze which seems more reasonable
          if (freshData.bedrooms < 10 && property.bedrooms >= 15) {
            console.log(`\n🎯 LIKELY ISSUE: Database has unrealistic high count (${property.bedrooms})`);
            console.log(`✅ Fresh scrape seems more reasonable (${freshData.bedrooms})`);
            console.log(`🔧 CONCLUSION: Previous extraction was wrong, current extraction is better`);
          } else if (freshData.bedrooms >= 15 && property.bedrooms < 10) {
            console.log(`\n🎯 LIKELY ISSUE: Fresh scrape has unrealistic high count (${freshData.bedrooms})`);
            console.log(`✅ Database seems more reasonable (${property.bedrooms})`);
            console.log(`🔧 CONCLUSION: Current extraction is wrong, needs fixing`);
          } else {
            console.log(`\n🤔 UNCLEAR: Both values seem unusual, manual verification needed`);
          }
        } else {
          console.log(`\n✅ Values match - but 15 bedrooms is still suspicious for a normal property`);
          console.log(`🔍 This might be a legitimate hostel/hotel listing`);
        }
        
      } else {
        console.log('❌ Fresh scraping failed - no data returned');
      }
      
    } catch (error) {
      console.error(`❌ Error scraping: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

testHostelProperty();
