// Debug Production vs Test Pipeline - Compare data flow
require('dotenv').config();

const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugProductionVsTest() {
  console.log('🔍 Debug: Production vs Test Pipeline');
  console.log('='.repeat(60));

  const testUrl = 'https://balicoconutliving.com/bali-villa-sale-freehold/Canggu/6268-V005-5796/Villa-Kimi-Berawa-B';
  
  console.log(`📍 Test URL: ${testUrl}`);
  console.log('');

  try {
    console.log('🚀 Step 1: Running production pipeline (runExtractBatch)...');
    const results = await runExtractBatch('bali_coconut_living', [testUrl], {});
    
    console.log('📊 Step 2: Analyzing production results...');
    
    if (results && results.processedResults) {
      const processed = results.processedResults;
      console.log(`   Found ${processed.length} processed results`);
      
      processed.forEach((result, i) => {
        console.log(`\n📋 Production Result ${i + 1}:`);
        console.log(`   Success: ${result.ok ? '✅' : '❌'}`);
        
        if (result.data) {
          const data = result.data;
          console.log(`   🏷️  Title: ${data.title || 'NULL'}`);
          console.log(`   🛏️  Bedrooms: ${data.bedrooms || 'NULL'}`);
          console.log(`   🚿 Bathrooms: ${data.bathrooms || 'NULL'}`);
          console.log(`   🅿️  Parking: ${data.parking_spaces || 'NULL'}`);
          console.log(`   📐 Size sqft: ${data.size_sqft || 'NULL'}`);
          console.log(`   🏞️  Lot size: ${data.lot_size_sqft || 'NULL'}`);
          console.log(`   📅 Year built: ${data.year_built || 'NULL'}`);
          console.log(`   💰 Price: ${data.price || 'NULL'}`);
          console.log(`   🏠 Ownership: ${data.ownership_type || 'NULL'}`);
          console.log(`   🔗 Source URL: ${data.source_url || 'NULL'}`);
          console.log(`   🆔 External ID: ${data.external_id || 'NULL'}`);
          
          // Check if data was actually saved to database
          if (result.id) {
            console.log(`   💾 Database ID: ${result.id}`);
            console.log(`   ✅ SAVED TO DATABASE`);
          } else {
            console.log(`   ❌ NOT SAVED TO DATABASE`);
          }
        }
      });
    }

    // Now check what was actually saved in the database
    console.log('\n🗄️  Step 3: Checking database for latest entry...');
    
    const { db, properties } = require('./drizzle_client');
    const { desc, eq } = require('drizzle-orm');
    
    const latest = await db
      .select({
        id: properties.id,
        title: properties.title,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        parking_spaces: properties.parking_spaces,
        size_sqft: properties.size_sqft,
        lot_size_sqft: properties.lot_size_sqft,
        year_built: properties.year_built,
        price: properties.price,
        ownership_type: properties.ownership_type,
        source_url: properties.source_url,
        external_id: properties.external_id,
        created_at: properties.created_at
      })
      .from(properties)
      .where(eq(properties.source_id, 'bali_coconut_living'))
      .orderBy(desc(properties.created_at))
      .limit(1);
    
    if (latest.length > 0) {
      const dbData = latest[0];
      console.log('\n📋 Latest Database Entry:');
      console.log(`   🏷️  Title: ${dbData.title || 'NULL'}`);
      console.log(`   🛏️  Bedrooms: ${dbData.bedrooms || 'NULL'}`);
      console.log(`   🚿 Bathrooms: ${dbData.bathrooms || 'NULL'}`);
      console.log(`   🅿️  Parking: ${dbData.parking_spaces || 'NULL'}`);
      console.log(`   📐 Size sqft: ${dbData.size_sqft || 'NULL'}`);
      console.log(`   🏞️  Lot size: ${dbData.lot_size_sqft || 'NULL'}`);
      console.log(`   📅 Year built: ${dbData.year_built || 'NULL'}`);
      console.log(`   💰 Price: ${dbData.price || 'NULL'}`);
      console.log(`   🏠 Ownership: ${dbData.ownership_type || 'NULL'}`);
      console.log(`   🔗 Source URL: ${dbData.source_url || 'NULL'}`);
      console.log(`   🆔 External ID: ${dbData.external_id || 'NULL'}`);
      console.log(`   📅 Created: ${dbData.created_at}`);
      
      // Compare with expected values
      const missingFields = [];
      if (!dbData.bedrooms) missingFields.push('bedrooms');
      if (!dbData.bathrooms) missingFields.push('bathrooms');
      if (!dbData.size_sqft) missingFields.push('size_sqft');
      if (!dbData.price) missingFields.push('price');
      if (!dbData.ownership_type) missingFields.push('ownership_type');
      if (!dbData.source_url) missingFields.push('source_url');
      
      if (missingFields.length > 0) {
        console.log(`\n❌ MISSING FIELDS IN DATABASE: ${missingFields.join(', ')}`);
      } else {
        console.log(`\n✅ ALL EXPECTED FIELDS PRESENT IN DATABASE`);
      }
    } else {
      console.log('\n❌ No database entry found');
    }

  } catch (error) {
    console.error('💥 Error during debug:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run if called directly
if (require.main === module) {
  debugProductionVsTest()
    .then(() => {
      console.log('\n🎯 Debug completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Debug failed:', error.message);
      process.exit(1);
    });
}

module.exports = { debugProductionVsTest };
