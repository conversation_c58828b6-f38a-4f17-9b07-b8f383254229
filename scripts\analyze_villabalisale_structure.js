// Analyze Villa Bali Sale structure to understand how to implement scraping
require('dotenv').config();
const { getKeyManager } = require('../scrape_worker/key_manager');

const keyManager = getKeyManager();

async function analyzeVillaBaliSaleStructure() {
  console.log('🔍 Analyzing Villa Bali Sale Structure\n');
  
  // Test different types of listing pages
  const testUrls = [
    'https://www.villabalisale.com/realestate-property/for-sale/villa/all/canggu',
    'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/seminyak',
    'https://www.villabalisale.com/fr/immobilier/a-vendre/villa/freehold/canggu'
  ];
  
  try {
    const currentKey = keyManager.getCurrentKey();
    console.log(`🔑 Using key: ${currentKey.maskedKey}`);
    
    for (let i = 0; i < testUrls.length; i++) {
      const url = testUrls[i];
      console.log(`\n🌐 Testing URL ${i + 1}/${testUrls.length}: ${url}`);
      
      // Use batch scrape to get clean content
      const response = await fetch('https://api.firecrawl.dev/v1/batch/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentKey.key}`
        },
        body: JSON.stringify({
          urls: [url],
          formats: ['json', 'markdown'],
          jsonOptions: {
            prompt: "Extract all property listings with their URLs, titles, prices, and locations. Look for individual property links that lead to detailed property pages."
          },
          onlyMainContent: true,
          timeout: 30000,
          maxConcurrency: 1,
          ignoreInvalidURLs: true,
          blockAds: true,
          proxy: 'auto',
          waitFor: 3000, // Wait longer for dynamic content
          removeBase64Images: true
        })
      });
      
      if (!response.ok) {
        console.log(`❌ Failed: HTTP ${response.status}`);
        continue;
      }
      
      const batchData = await response.json();
      console.log(`✅ Batch scrape initiated: ${batchData.id}`);
      
      // Poll for results
      let attempts = 0;
      const maxAttempts = 10;
      
      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 15000));
        attempts++;
        
        const statusResponse = await fetch(`https://api.firecrawl.dev/v1/batch/scrape/${batchData.id}`, {
          headers: {
            'Authorization': `Bearer ${currentKey.key}`
          }
        });
        
        if (!statusResponse.ok) {
          console.log(`⚠️  Status check failed: ${statusResponse.status}`);
          continue;
        }
        
        const statusData = await statusResponse.json();
        console.log(`📊 Status: ${statusData.status}, Completed: ${statusData.completed}/${statusData.total}`);
        
        if (statusData.status === 'completed') {
          if (statusData.data && statusData.data.length > 0) {
            const result = statusData.data[0];
            
            console.log('\n📄 Analysis Results:');
            console.log('='.repeat(50));
            
            if (result.json) {
              console.log('📊 JSON Data:');
              console.log(JSON.stringify(result.json, null, 2));
            }
            
            if (result.markdown) {
              console.log('\n📝 Markdown Content (first 1000 chars):');
              console.log(result.markdown.substring(0, 1000));
              
              // Look for property URLs in markdown
              const propertyUrlPattern = /https:\/\/www\.villabalisale\.com\/[^\/]+\/[^\/]+\/[^\/]+\/[^\/]+\/[^\s\)]+/g;
              const propertyUrls = result.markdown.match(propertyUrlPattern);
              
              if (propertyUrls) {
                console.log('\n🔗 Found Property URLs:');
                const uniqueUrls = [...new Set(propertyUrls)];
                uniqueUrls.slice(0, 10).forEach((url, i) => {
                  console.log(`${i + 1}. ${url}`);
                });
                console.log(`Total unique URLs found: ${uniqueUrls.length}`);
              } else {
                console.log('\n❌ No property URLs found in markdown');
              }
            }
            
          } else {
            console.log('❌ No data returned');
          }
          break;
        } else if (statusData.status === 'failed') {
          console.log('❌ Batch scrape failed');
          break;
        }
      }
      
      if (attempts >= maxAttempts) {
        console.log('⏰ Polling timeout');
      }
      
      // Wait between URLs
      if (i < testUrls.length - 1) {
        console.log('⏳ Waiting 30 seconds before next URL...');
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }
    
    // Analyze sitemap structure
    console.log('\n📋 Sitemap Analysis:');
    console.log('='.repeat(50));
    
    const sitemapResponse = await fetch('https://www.villabalisale.com/sitemap_realestate.xml');
    const sitemapText = await sitemapResponse.text();
    
    // Extract different URL patterns
    const urlPatterns = {
      'French Rental Annual': /https:\/\/www\.villabalisale\.com\/fr\/immobilier\/a-louer\/villa\/annuel\/[^<]+/g,
      'French Rental Weekly': /https:\/\/www\.villabalisale\.com\/fr\/immobilier\/a-louer\/villa\/hebdomadaire\/[^<]+/g,
      'French Rental Daily': /https:\/\/www\.villabalisale\.com\/fr\/immobilier\/a-louer\/villa\/journalier\/[^<]+/g,
      'English Sale All': /https:\/\/www\.villabalisale\.com\/realestate-property\/for-sale\/villa\/all\/[^<]+/g,
      'English Sale Freehold': /https:\/\/www\.villabalisale\.com\/realestate-property\/for-sale\/villa\/freehold\/[^<]+/g,
      'English Sale Leasehold': /https:\/\/www\.villabalisale\.com\/realestate-property\/for-sale\/villa\/leasehold\/[^<]+/g
    };
    
    Object.keys(urlPatterns).forEach(patternName => {
      const matches = sitemapText.match(urlPatterns[patternName]);
      console.log(`${patternName}: ${matches ? matches.length : 0} URLs`);
      if (matches && matches.length > 0) {
        console.log(`  Example: ${matches[0]}`);
      }
    });
    
    console.log('\n🎯 Recommended Implementation Strategy:');
    console.log('='.repeat(50));
    console.log('1. **Listing Page Crawler**: Scrape listing pages to find individual property URLs');
    console.log('2. **Focus on English URLs**: Start with /realestate-property/ URLs (easier to parse)');
    console.log('3. **Prioritize Sale Properties**: Focus on for-sale listings first');
    console.log('4. **Location-based**: Process by location (canggu, seminyak, etc.)');
    console.log('5. **Two-step process**: ');
    console.log('   - Step 1: Scrape listing pages → extract property URLs → add to queue');
    console.log('   - Step 2: Use existing scraping system to process individual properties');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    console.error(error.stack);
  }
}

analyzeVillaBaliSaleStructure();
