// Debug the specific property to see what JSON data Villa Bali Sale provides
require('dotenv').config();
const { getKeyManager } = require('../scrape_worker/key_manager');

async function debugSpecificProperty() {
  console.log('🔍 Debugging Specific Villa Bali Sale Property\n');
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/amed/homey-off-plan-villa-in-amed-east-bali-for-sale';
  
  try {
    console.log(`🔄 Scraping: ${testUrl}`);
    
    const keyManager = getKeyManager();
    const currentKey = keyManager.getCurrentKey();
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${currentKey.key}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['json', 'markdown', 'html'],
        jsonOptions: {
          prompt: "Extract ALL property information including property ID, ownership type (freehold/leasehold), lease duration, land size, building size, parking, year built, description, and any other property details. Be extremely detailed and include all available information."
        },
        onlyMainContent: false,
        timeout: 60000,
        waitFor: 15000,
        removeBase64Images: true,
        blockAds: true,
        proxy: 'auto'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    console.log('\n📊 Scrape Results:');
    console.log('='.repeat(60));
    console.log(`Success: ${!!data.data}`);
    console.log(`Has JSON: ${!!data.data?.json}`);
    console.log(`Has Markdown: ${!!data.data?.markdown}`);
    console.log(`Has HTML: ${!!data.data?.html}`);
    
    if (data.data?.json) {
      console.log('\n🔍 COMPLETE JSON Content:');
      console.log('-'.repeat(60));
      console.log(JSON.stringify(data.data.json, null, 2));
      
      const json = data.data.json;
      
      console.log('\n🎯 KEY FIELDS ANALYSIS:');
      console.log('='.repeat(60));
      console.log(`Title: ${json.title || 'MISSING'}`);
      console.log(`Price: ${json.price || 'MISSING'}`);
      console.log(`Property ID: ${json.propertyId || json.id || json.reference || json.code || 'MISSING'}`);
      console.log(`Ownership: ${json.ownership || json.ownershipType || json.tenure || json.leasehold || json.freehold || 'MISSING'}`);
      console.log(`Land Size: ${json.landSize || json.land || json.lotSize || json.plot || 'MISSING'}`);
      console.log(`Building Size: ${json.buildingSize || json.building || json.size || json.area || 'MISSING'}`);
      console.log(`Bedrooms: ${json.bedrooms || 'MISSING'}`);
      console.log(`Bathrooms: ${json.bathrooms || 'MISSING'}`);
      console.log(`Parking: ${json.parking || json.parkingSpaces || json.garage || 'MISSING'}`);
      console.log(`Year Built: ${json.yearBuilt || json.built || json.construction || 'MISSING'}`);
      console.log(`Description: ${json.description ? json.description.substring(0, 100) + '...' : 'MISSING'}`);
      
      if (json.propertyDetails) {
        console.log('\n📋 Property Details Object:');
        console.log(JSON.stringify(json.propertyDetails, null, 2));
      }
      
      if (json.features) {
        console.log('\n🏠 Features Object:');
        console.log(JSON.stringify(json.features, null, 2));
      }
      
      if (json.specifications) {
        console.log('\n📐 Specifications Object:');
        console.log(JSON.stringify(json.specifications, null, 2));
      }
    }
    
    if (data.data?.markdown) {
      console.log('\n📝 Markdown Content (first 1000 chars):');
      console.log('-'.repeat(60));
      console.log(data.data.markdown.substring(0, 1000) + '...');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

debugSpecificProperty();
