// Debug location parsing for land property
require('dotenv').config();

async function debugLocationParsing() {
  const testUrl = 'https://betterplace.cc/buy/properties/BPLF02065';
  const apiKey = process.env.FIRECRAWL_API_KEY;
  
  try {
    console.log(`🔍 Testing location parsing for: ${testUrl}`);
    
    // Start scrape job
    const response = await fetch('https://api.firecrawl.dev/v1/batch/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        urls: [testUrl],
        formats: ['markdown'],
        onlyMainContent: true,
        timeout: 45000,
        ignoreInvalidURLs: true,
        blockAds: true,
        proxy: 'auto',
        waitFor: 2000,
        removeBase64Images: true
      })
    });

    const result = await response.json();
    const jobId = result.id;
    
    // Wait and poll
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    const pollResponse = await fetch(`https://api.firecrawl.dev/v1/batch/scrape/${jobId}`, {
      headers: { 'Authorization': `Bearer ${apiKey}` }
    });
    
    const pollResult = await pollResponse.json();
    
    if (pollResult.status === 'completed' && pollResult.data?.[0]?.markdown) {
      const markdown = pollResult.data[0].markdown;
      
      console.log(`📝 Full markdown content:`);
      console.log('='.repeat(80));
      console.log(markdown);
      console.log('='.repeat(80));
      
      // Test location regex patterns
      console.log(`\n🔍 Testing location patterns:`);
      
      const patterns = [
        /(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi|Tabanan|Badung|Gianyar)/i,
        /Location[:\s]*([^\n]+)/i,
        /Address[:\s]*([^\n]+)/i,
        /Prime\s+([A-Za-z\s]+)\s+Location/i,
        /in\s+([A-Za-z\s]+)\s+Location/i,
        /([A-Za-z\s]+)\s+–\s+Pink\s+Tourism\s+Zone/i
      ];
      
      patterns.forEach((pattern, index) => {
        const match = markdown.match(pattern);
        console.log(`Pattern ${index + 1}: ${pattern} -> ${match ? match[1] || match[0] : 'NO MATCH'}`);
      });
      
      // Test property ID extraction
      const propertyIdMatch = testUrl.match(/\/([A-Z0-9]+)$/i);
      const property_id = propertyIdMatch ? propertyIdMatch[1] : 'unknown';
      console.log(`\n🆔 Property ID: ${property_id}`);
      
      // Test title extraction
      const titleMatch = markdown.match(/^#\s+(.+)$/m) ||
                        markdown.match(/^##\s+(.+)$/m) ||
                        markdown.match(/!\[([^\]]+)\]/) ||
                        markdown.match(/\*\*([^*]+)\*\*/);
      const title = titleMatch ? titleMatch[1].trim() : 'Property Title Not Found';
      console.log(`📋 Title: ${title}`);
      
    } else {
      console.error(`❌ Failed to get markdown content`);
    }
    
  } catch (error) {
    console.error(`❌ Debug failed:`, error.message);
  }
}

debugLocationParsing();
