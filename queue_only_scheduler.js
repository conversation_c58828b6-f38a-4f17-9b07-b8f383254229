// Queue-Only Scheduler - Only processes existing queue items
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');

class QueueOnlyScheduler {
  constructor() {
    this.queueManager = new QueueManager();
    this.isRunning = false;
  }

  // Process queue every 3 minutes
  async processQueueJob() {
    if (this.isRunning) {
      console.log('⏳ Queue processing already running, skipping...');
      return;
    }

    this.isRunning = true;
    console.log('\n🕐 [QUEUE-SCHEDULER] Processing queue...');

    try {
      const websites = ['betterplace', 'bali_home_immo', 'bali_villa_realty'];
      let totalProcessed = 0;

      for (const website of websites) {
        console.log(`📦 Processing ${website} queue...`);
        
        try {
          const result = await this.queueManager.processQueue(website, 3); // 3 items per website
          const processed = result?.processed || 0;
          totalProcessed += processed;
          
          if (processed > 0) {
            console.log(`✅ ${website}: ${processed} URLs processed`);
          } else {
            console.log(`ℹ️  ${website}: No pending URLs`);
          }
        } catch (error) {
          console.error(`❌ Error processing ${website}:`, error.message);
        }
        
        // Wait between websites
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      if (totalProcessed > 0) {
        console.log(`✅ [QUEUE-SCHEDULER] Total processed: ${totalProcessed} URLs`);
        
        // Check total properties
        const { db, properties } = require('./drizzle_client');
        const totalProps = await db.select().from(properties);
        console.log(`🏠 Total properties now: ${totalProps.length}`);
      } else {
        console.log(`ℹ️  [QUEUE-SCHEDULER] No URLs to process`);
      }

    } catch (error) {
      console.error('❌ [QUEUE-SCHEDULER] Queue processing failed:', error.message);
    } finally {
      this.isRunning = false;
    }
  }

  // Check queue status
  async checkQueueStatus() {
    try {
      const { db, scrapingQueue } = require('./drizzle_client');
      const { eq } = require('drizzle-orm');
      
      const pending = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'pending'));
      const processing = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'processing'));
      
      console.log(`\n📊 [QUEUE-STATUS] Pending: ${pending.length}, Processing: ${processing.length}`);
      
      if (pending.length > 0) {
        console.log('📋 Next URLs to process:');
        pending.slice(0, 3).forEach((item, index) => {
          console.log(`   ${index + 1}. ${item.website_id}: ${item.url.substring(0, 50)}...`);
        });
      }
      
    } catch (error) {
      console.error('❌ Error checking queue status:', error.message);
    }
  }

  // Start the queue-only scheduler
  start() {
    console.log('🚀 [QUEUE-SCHEDULER] Starting queue-only scheduler...');
    console.log('📅 Schedule: Process queue every 3 minutes');
    console.log('⚠️  Note: This scheduler only processes existing queue items');
    console.log('   To add new URLs, use crawling or manual addition');
    console.log('');

    // Initial status check
    this.checkQueueStatus();

    // Process queue every 3 minutes
    const queueInterval = setInterval(() => {
      this.processQueueJob();
    }, 3 * 60 * 1000); // 3 minutes

    // Status check every 10 minutes
    const statusInterval = setInterval(() => {
      this.checkQueueStatus();
    }, 10 * 60 * 1000); // 10 minutes

    // Initial processing after 10 seconds
    setTimeout(() => this.processQueueJob(), 10000);

    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 [QUEUE-SCHEDULER] Shutting down...');
      clearInterval(queueInterval);
      clearInterval(statusInterval);
      process.exit(0);
    });

    console.log('✅ [QUEUE-SCHEDULER] Queue-only scheduler started');
    console.log('   Press Ctrl+C to stop');
  }

  // Manual trigger for testing
  async runOnce() {
    console.log('🧪 [QUEUE-SCHEDULER] Running queue processing once...');
    
    await this.checkQueueStatus();
    await this.processQueueJob();
    
    console.log('✅ [QUEUE-SCHEDULER] Test run completed');
    process.exit(0);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const scheduler = new QueueOnlyScheduler();

  switch (command) {
    case 'start':
      scheduler.start();
      break;
      
    case 'test':
      await scheduler.runOnce();
      break;
      
    case 'status':
      await scheduler.checkQueueStatus();
      process.exit(0);
      break;
      
    default:
      console.log('Queue-Only Scheduler');
      console.log('');
      console.log('Commands:');
      console.log('  start  - Start the continuous queue processor');
      console.log('  test   - Process queue once for testing');
      console.log('  status - Check current queue status');
      console.log('');
      console.log('Example: node queue_only_scheduler.js start');
  }
}

if (require.main === module) {
  main();
}

module.exports = { QueueOnlyScheduler };
