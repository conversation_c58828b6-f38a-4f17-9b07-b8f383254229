require('dotenv').config();
const { Pool } = require('pg');

async function checkNewResults() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🔍 Checking new scraping results...');
    
    const result = await pool.query(`
      SELECT 
        external_id,
        title,
        bedrooms,
        bathrooms,
        size_sqft,
        lot_size_sqft,
        year_built,
        ownership_type,
        lease_duration_years,
        created_at
      FROM property 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ No properties found in database');
    } else {
      console.log(`✅ Found ${result.rows.length} properties:`);
      console.log('\n📊 NEW SCRAPING RESULTS:');
      
      result.rows.forEach((property, index) => {
        console.log(`\n${index + 1}. ${property.external_id}:`);
        console.log(`   Title: ${property.title}`);
        console.log(`   Bedrooms: ${property.bedrooms}`);
        console.log(`   Bathrooms: ${property.bathrooms}`);
        console.log(`   Building Size: ${property.size_sqft} sqft`);
        console.log(`   Land Size: ${property.lot_size_sqft} sqft`);
        console.log(`   Year Built: ${property.year_built}`);
        console.log(`   Ownership: ${property.ownership_type}`);
        console.log(`   Lease: ${property.lease_duration_years} years`);
        console.log(`   Created: ${property.created_at}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkNewResults();
