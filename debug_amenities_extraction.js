const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugAmenitiesExtraction() {
  try {
    console.log('🔍 DEBUGGING AMENITIES EXTRACTION...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    console.log('URL:', testUrl);
    console.log('Expected: NO Spa amenity (land property)\n');
    
    // First, let's scrape and see the raw markdown
    console.log('1. SCRAPING RAW MARKDOWN:');
    const results = await runExtractBatch('betterplace', [testUrl], {});
    
    if (results && results.extractedData && results.extractedData.length > 0) {
      const rawData = results.extractedData[0];
      
      console.log('✅ Raw extraction successful');
      console.log('- Title:', rawData.title);
      console.log('- Property Type:', rawData.property_type);
      console.log('- Amenities:', rawData.amenities);
      console.log('- Description length:', rawData.description?.length || 0);
      console.log('- Description preview:', rawData.description?.substring(0, 200));
      
      // Let's also check what's in the markdown that might trigger "Spa"
      console.log('\n2. CHECKING FOR SPA KEYWORDS IN CONTENT:');
      const allText = `${rawData.title || ''} ${rawData.description || ''}`.toLowerCase();
      
      const spaKeywords = ['spa', 'massage', 'wellness', 'treatment', 'therapy'];
      spaKeywords.forEach(keyword => {
        if (allText.includes(keyword)) {
          console.log(`⚠️  Found "${keyword}" in content`);
          // Find the context
          const index = allText.indexOf(keyword);
          const context = allText.substring(Math.max(0, index - 50), index + 50);
          console.log(`   Context: ...${context}...`);
        } else {
          console.log(`✅ "${keyword}" NOT found in content`);
        }
      });
      
      // Check if it's a land property issue
      console.log('\n3. LAND PROPERTY VALIDATION:');
      console.log('- Property ID from URL:', testUrl.match(/([A-Z0-9]+)$/)?.[1]);
      console.log('- Detected property type:', rawData.property_type);
      console.log('- Should be land:', testUrl.includes('BPLF'));
      console.log('- Has bedrooms:', rawData.bedrooms);
      console.log('- Has bathrooms:', rawData.bathrooms);
      console.log('- Land size:', rawData.size?.land_size_sqm);
      
    } else {
      console.log('❌ No extraction results');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

debugAmenitiesExtraction();
