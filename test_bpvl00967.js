require('dotenv').config();
const { Pool } = require('pg');
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testBPVL00967() {
  console.log('🔍 TESTING BPVL00967 (The 5 bed/5 bath property)');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Get BPVL00967 from database
    const result = await pool.query(`
      SELECT 
        external_id,
        title,
        bedrooms,
        bathrooms,
        source_url
      FROM property 
      WHERE external_id = 'BPVL00967'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ BPVL00967 not found in database');
      return;
    }
    
    const property = result.rows[0];
    
    console.log(`🏠 BPVL00967 DATABASE VALUES:`);
    console.log(`📍 URL: ${property.source_url}`);
    console.log(`💾 Database: ${property.bedrooms} bed / ${property.bathrooms} bath`);
    console.log(`📝 Title: ${property.title}`);
    
    console.log('\n🔄 RE-SCRAPING WITH CURRENT MAPPER...');
    
    try {
      // Use the actual scraper to get fresh data
      const scrapingResult = await runExtractBatch('betterplace', [property.source_url], {});
      
      if (scrapingResult && scrapingResult.extractedData && scrapingResult.extractedData.length > 0) {
        const freshData = scrapingResult.extractedData[0];
        
        console.log(`\n🆕 FRESH SCRAPE RESULTS:`);
        console.log(`   Bedrooms: ${freshData.bedrooms}`);
        console.log(`   Bathrooms: ${freshData.bathrooms}`);
        console.log(`   Title: ${freshData.title}`);
        console.log(`   Price: ${freshData.price}`);
        
        // Compare
        const bedroomMatch = freshData.bedrooms === property.bedrooms;
        const bathroomMatch = freshData.bathrooms === property.bathrooms;
        
        console.log(`\n📊 COMPARISON:`);
        console.log(`   Bedrooms: ${property.bedrooms} (DB) vs ${freshData.bedrooms} (Fresh) ${bedroomMatch ? '✅' : '❌'}`);
        console.log(`   Bathrooms: ${property.bathrooms} (DB) vs ${freshData.bathrooms} (Fresh) ${bathroomMatch ? '✅' : '❌'}`);
        
        if (!bedroomMatch || !bathroomMatch) {
          console.log(`\n❌ MISMATCH DETECTED!`);
          console.log(`🎯 EXPECTED: 5 bedrooms, 5 bathrooms (from manual check)`);
          console.log(`💾 DATABASE: ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms`);
          console.log(`🆕 FRESH: ${freshData.bedrooms} bedrooms, ${freshData.bathrooms} bathrooms`);
          
          // Determine which is correct
          if (freshData.bedrooms === 5 && freshData.bathrooms === 5) {
            console.log(`✅ Fresh scrape is CORRECT! Database needs update.`);
          } else if (property.bedrooms === 5 && property.bathrooms === 5) {
            console.log(`✅ Database is CORRECT! Mapper needs fix.`);
          } else {
            console.log(`❌ Neither matches expected 5/5. Need to investigate.`);
          }
        } else {
          console.log(`\n✅ Perfect match! No issues detected.`);
        }
        
      } else {
        console.log('❌ Fresh scraping failed');
      }
      
    } catch (error) {
      console.error(`❌ Error scraping BPVL00967: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

testBPVL00967();
