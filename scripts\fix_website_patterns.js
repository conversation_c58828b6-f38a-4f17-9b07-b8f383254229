// Fix website patterns with proper JSON
require('dotenv').config();
const postgres = require('postgres');

const client = postgres(process.env.SUPABASE_DATABASE_URL, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

async function fixWebsitePatterns() {
  console.log('🔧 Fixing website patterns with proper JSON...\n');
  
  try {
    // BetterPlace patterns
    const betterplacePatterns = {
      property_patterns: ["/buy/properties/BPVL\\d+$", "/rent/properties/BPVR\\d+$"],
      listing_patterns: ["/buy/properties$", "/rent/properties$", "/buy/properties\\?", "/rent/properties\\?", "/buy/indonesia/", "/rent/indonesia/"],
      keywords: ["bedroom", "bathroom", "villa", "apartment", "price", "sqm"]
    };
    
    await client`
      UPDATE website_configs 
      SET property_url_patterns = ${JSON.stringify(betterplacePatterns)}
      WHERE website_id = 'betterplace'
    `;
    console.log('✅ Updated BetterPlace patterns');
    
    // Bali Villa Realty patterns
    const baliVillaPatterns = {
      property_patterns: ["/property/[^/]+/$"],
      listing_patterns: ["/properties$", "/properties/", "/search", "/category/", "/type/"],
      keywords: ["bedroom", "villa", "sale", "rent", "leasehold", "freehold", "bali"]
    };
    
    await client`
      UPDATE website_configs 
      SET property_url_patterns = ${JSON.stringify(baliVillaPatterns)}
      WHERE website_id = 'bali_villa_realty'
    `;
    console.log('✅ Updated Bali Villa Realty patterns');
    
    // Bali Home Immo patterns
    const baliHomePatterns = {
      property_patterns: ["/realestate-property/[^/]+/$"],
      listing_patterns: ["/realestate-property/$", "/realestate-property/for-", "/search", "/category/"],
      keywords: ["bedroom", "villa", "apartment", "sale", "rent", "leasehold", "freehold", "bali"]
    };
    
    await client`
      UPDATE website_configs 
      SET property_url_patterns = ${JSON.stringify(baliHomePatterns)}
      WHERE website_id = 'bali_home_immo'
    `;
    console.log('✅ Updated Bali Home Immo patterns');
    
    // Verify the updates
    console.log('\n📋 Verified website configurations:');
    const configs = await client`
      SELECT website_id, name, property_url_patterns
      FROM website_configs 
      WHERE sitemap_enabled = TRUE
      ORDER BY website_id
    `;
    
    configs.forEach(config => {
      console.log(`\n🌐 ${config.website_id}:`);
      try {
        const patterns = JSON.parse(config.property_url_patterns);
        console.log(`   Property patterns: ${patterns.property_patterns?.length || 0}`);
        if (patterns.property_patterns) {
          patterns.property_patterns.forEach((pattern, i) => {
            console.log(`     ${i + 1}. ${pattern}`);
          });
        }
        console.log(`   Listing patterns: ${patterns.listing_patterns?.length || 0}`);
        if (patterns.listing_patterns) {
          patterns.listing_patterns.slice(0, 3).forEach((pattern, i) => {
            console.log(`     ${i + 1}. ${pattern}`);
          });
          if (patterns.listing_patterns.length > 3) {
            console.log(`     ... and ${patterns.listing_patterns.length - 3} more`);
          }
        }
        console.log(`   Keywords: ${patterns.keywords?.length || 0}`);
      } catch (error) {
        console.log(`   ❌ Invalid JSON patterns: ${error.message}`);
      }
    });
    
    console.log('\n🎉 Website patterns updated successfully!');
    
  } catch (error) {
    console.error('❌ Pattern update failed:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

fixWebsitePatterns();
