// Debug what the real scraping actually produces
require('dotenv').config();
const { runExtractBatch } = require('../scrape_worker/run_batch');

async function debugRealScrapeOutput() {
  console.log('🔍 Debugging Real Scrape Output\n');
  
  try {
    const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/amed/homey-off-plan-villa-in-amed-east-bali-for-sale';
    
    console.log('🔄 Scraping URL with debug output...');
    
    // Temporarily modify the mapper to log debug info
    const originalMapper = require('../scrape_worker/mappers').mapVillaBaliSale;
    
    // Override the mapper to add debug logging
    require('../scrape_worker/mappers').mapVillaBaliSale = async function(raw) {
      console.log('\n🔍 DEBUG: Raw JSON Data:');
      console.log(JSON.stringify(raw.json, null, 2));
      
      const result = await originalMapper(raw);
      
      console.log('\n🔍 DEBUG: Mapped Result:');
      console.log('Title:', result.title);
      console.log('Description:', result.description ? 'PRESENT (' + result.description.length + ' chars)' : 'MISSING');
      console.log('Size sqft:', result.size_sqft || 'MISSING');
      console.log('Lot size sqft:', result.lot_size_sqft || 'MISSING');
      console.log('Parking:', result.parking_spaces || 'MISSING');
      console.log('External ID:', result.media?.external_id || 'MISSING');
      console.log('Ownership:', result.amenities?.ownership_type || 'MISSING');
      
      return result;
    };
    
    const results = await runExtractBatch('villabalisale.com', [testUrl], {});
    
    console.log('\n📊 Final Result:');
    if (results.length > 0) {
      if (results[0].success) {
        console.log('✅ SUCCESS - Property processed');
      } else {
        console.log('❌ FAILED:', results[0].error);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

debugRealScrapeOutput();
