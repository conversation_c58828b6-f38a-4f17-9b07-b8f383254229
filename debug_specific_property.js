const { db, properties, discoveredUrls } = require('./drizzle_client');
const { eq } = require('drizzle-orm');
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugSpecificProperty() {
  try {
    console.log('🔍 DEBUGGING SPECIFIC PROPERTY ISSUES...\n');
    
    // 1. Find the property in database
    console.log('1. CHECKING DATABASE PROPERTY:');
    const dbProperty = await db
      .select()
      .from(properties)
      .where(eq(properties.id, '08d10b8f-8000-46c9-9e1a-7f4f729ea6e5'));
    
    if (dbProperty.length > 0) {
      const prop = dbProperty[0];
      console.log('✅ Property found in database:');
      console.log('- ID:', prop.id);
      console.log('- Title:', prop.title);
      console.log('- Type:', prop.type);
      console.log('- Category:', prop.category);
      console.log('- Description length:', prop.description?.length || 0);
      console.log('- Description:', prop.description);
      console.log('- Amenities:', prop.amenities);
      console.log('- Source URL ID:', prop.source_url_id);
      console.log('- Source URL:', prop.source_url);
      console.log('- Created:', prop.created_at);
      
      // 2. If we have source_url, try to re-scrape it
      if (prop.source_url) {
        console.log('\n' + '='.repeat(80));
        console.log('2. RE-SCRAPING THE SOURCE URL:');
        console.log('URL:', prop.source_url);
        
        try {
          const results = await runExtractBatch('betterplace', [prop.source_url], {});
          
          if (results && results.processedResults && results.processedResults.length > 0) {
            const freshData = results.processedResults[0];
            console.log('\n✅ Fresh scraping results:');
            console.log('- Title:', freshData.title);
            console.log('- Type:', freshData.type);
            console.log('- Category:', freshData.category);
            console.log('- Description length:', freshData.description?.length || 0);
            console.log('- Description:', freshData.description);
            console.log('- Amenities:', freshData.amenities);
            
            // Compare with database
            console.log('\n📊 COMPARISON:');
            console.log('- Type: DB =', prop.type, '| Fresh =', freshData.type);
            console.log('- Category: DB =', prop.category, '| Fresh =', freshData.category);
            console.log('- Description: DB =', prop.description?.substring(0, 50), '| Fresh =', freshData.description?.substring(0, 50));
            console.log('- Amenities: DB =', JSON.stringify(prop.amenities), '| Fresh =', JSON.stringify(freshData.amenities));
            
          } else {
            console.log('❌ No results from fresh scraping');
          }
          
        } catch (scrapeError) {
          console.log('❌ Fresh scraping failed:', scrapeError.message);
        }
      }
      
    } else {
      console.log('❌ Property not found in database!');
    }
    
    // 3. Check if there's a discovered_url for this property
    console.log('\n' + '='.repeat(80));
    console.log('3. CHECKING DISCOVERED URLS:');
    
    const discoveredUrl = await db
      .select()
      .from(discoveredUrls)
      .where(eq(discoveredUrls.id, dbProperty[0]?.source_url_id || 'none'));
    
    if (discoveredUrl.length > 0) {
      console.log('✅ Found discovered URL:');
      console.log('- ID:', discoveredUrl[0].id);
      console.log('- URL:', discoveredUrl[0].url);
      console.log('- Website ID:', discoveredUrl[0].website_id);
      console.log('- URL Type:', discoveredUrl[0].url_type);
      console.log('- Is Property Page:', discoveredUrl[0].is_property_page);
      console.log('- Scrape Status:', discoveredUrl[0].scrape_status);
    } else {
      console.log('❌ No discovered URL found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

debugSpecificProperty();
