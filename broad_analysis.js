require('dotenv').config();
const { Pool } = require('pg');

async function broadAnalysis() {
  console.log('🔍 BROAD ANALYSIS: Looking for potential mismatches');
  console.log('='.repeat(60));
  
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    // Look for properties with unusual bedroom/bathroom combinations
    console.log('📊 ANALYZING BEDROOM/BATHROOM PATTERNS...\n');
    
    // Get distribution of bedroom/bathroom combinations
    const distributionResult = await pool.query(`
      SELECT 
        bedrooms,
        bathrooms,
        COUNT(*) as count,
        ARRAY_AGG(external_id ORDER BY created_at DESC) as sample_ids
      FROM property 
      WHERE bedrooms IS NOT NULL 
        AND bathrooms IS NOT NULL
        AND source_url LIKE '%betterplace.cc%'
        AND created_at > NOW() - INTERVAL '24 hours'
      GROUP BY bedrooms, bathrooms
      ORDER BY count DESC, bedrooms, bathrooms
    `);
    
    console.log('🏠 BEDROOM/BATHROOM DISTRIBUTION (last 24 hours):');
    distributionResult.rows.forEach(row => {
      const sampleIds = row.sample_ids.slice(0, 3).join(', ');
      console.log(`   ${row.bedrooms} bed / ${row.bathrooms} bath: ${row.count} properties (e.g., ${sampleIds})`);
    });
    
    // Look for suspicious patterns
    console.log('\n🚨 LOOKING FOR SUSPICIOUS PATTERNS:');
    
    // Properties with very high bedroom/bathroom counts
    const highCountResult = await pool.query(`
      SELECT external_id, bedrooms, bathrooms, title, source_url
      FROM property 
      WHERE (bedrooms > 10 OR bathrooms > 10)
        AND source_url LIKE '%betterplace.cc%'
        AND created_at > NOW() - INTERVAL '7 days'
      ORDER BY bedrooms DESC, bathrooms DESC
      LIMIT 5
    `);
    
    if (highCountResult.rows.length > 0) {
      console.log('\n🔍 Properties with >10 bedrooms or bathrooms:');
      highCountResult.rows.forEach(row => {
        console.log(`   ${row.external_id}: ${row.bedrooms}/${row.bathrooms} - ${row.title}`);
        console.log(`      URL: ${row.source_url}`);
      });
    } else {
      console.log('   ✅ No properties with >10 bedrooms/bathrooms found');
    }
    
    // Properties with unusual ratios (e.g., 1 bedroom but 5 bathrooms)
    const unusualRatioResult = await pool.query(`
      SELECT external_id, bedrooms, bathrooms, title, source_url
      FROM property 
      WHERE bedrooms IS NOT NULL 
        AND bathrooms IS NOT NULL
        AND source_url LIKE '%betterplace.cc%'
        AND created_at > NOW() - INTERVAL '7 days'
        AND (
          (bedrooms = 1 AND bathrooms > 3) OR
          (bedrooms = 2 AND bathrooms > 4) OR
          (bathrooms > bedrooms * 2)
        )
      ORDER BY (bathrooms::float / bedrooms::float) DESC
      LIMIT 5
    `);
    
    if (unusualRatioResult.rows.length > 0) {
      console.log('\n🔍 Properties with unusual bedroom/bathroom ratios:');
      unusualRatioResult.rows.forEach(row => {
        const ratio = (row.bathrooms / row.bedrooms).toFixed(1);
        console.log(`   ${row.external_id}: ${row.bedrooms}/${row.bathrooms} (ratio: ${ratio}) - ${row.title}`);
        console.log(`      URL: ${row.source_url}`);
      });
    } else {
      console.log('   ✅ No properties with unusual ratios found');
    }
    
    // Properties that might have extraction issues (title mentions different numbers)
    const titleMismatchResult = await pool.query(`
      SELECT external_id, bedrooms, bathrooms, title, source_url
      FROM property 
      WHERE bedrooms IS NOT NULL 
        AND bathrooms IS NOT NULL
        AND source_url LIKE '%betterplace.cc%'
        AND created_at > NOW() - INTERVAL '7 days'
        AND (
          (title ~* '\\d+\\s*bedroom' AND NOT title ~* bedrooms::text || '\\s*bedroom') OR
          (title ~* '\\d+\\s*bathroom' AND NOT title ~* bathrooms::text || '\\s*bathroom')
        )
      LIMIT 10
    `);
    
    if (titleMismatchResult.rows.length > 0) {
      console.log('\n🔍 Properties where title mentions different bedroom/bathroom counts:');
      titleMismatchResult.rows.forEach(row => {
        console.log(`   ${row.external_id}: DB(${row.bedrooms}/${row.bathrooms}) - ${row.title}`);
        console.log(`      URL: ${row.source_url}`);
      });
    } else {
      console.log('   ✅ No title mismatches found');
    }
    
    console.log('\n📈 SUMMARY:');
    console.log(`   Total recent properties analyzed: ${distributionResult.rows.reduce((sum, row) => sum + parseInt(row.count), 0)}`);
    console.log(`   Unique bedroom/bathroom combinations: ${distributionResult.rows.length}`);
    console.log(`   Suspicious high counts: ${highCountResult.rows.length}`);
    console.log(`   Unusual ratios: ${unusualRatioResult.rows.length}`);
    console.log(`   Title mismatches: ${titleMismatchResult.rows.length}`);
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

broadAnalysis();
