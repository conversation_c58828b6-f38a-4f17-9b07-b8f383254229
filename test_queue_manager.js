// Test Queue Manager - Direct test of production scraper
require('dotenv').config();

const { QueueManager } = require('./scrape_worker/queue_manager');

async function testQueueManager() {
  console.log('🧪 Testing Queue Manager - Production Scraper');
  console.log('='.repeat(60));

  try {
    const queueManager = new QueueManager();
    
    console.log('🎯 Processing 1 URL from bali_coconut_living queue...');
    
    // Process just 1 URL from Bali Coconut Living
    const result = await queueManager.processQueue('bali_coconut_living', 1);
    
    console.log('📊 Result:', result);
    
    if (result && result.processed > 0) {
      console.log('✅ SUCCESS: Queue manager processed URLs and saved to database');
      
      // Check the latest property
      const { db, properties } = require('./drizzle_client');
      const { desc, eq } = require('drizzle-orm');
      
      const latest = await db
        .select({
          id: properties.id,
          title: properties.title,
          source_id: properties.source_id,
          source_url: properties.source_url,
          created_at: properties.created_at
        })
        .from(properties)
        .where(eq(properties.source_id, 'bali_coconut_living'))
        .orderBy(desc(properties.created_at))
        .limit(1);
      
      if (latest.length > 0) {
        const prop = latest[0];
        console.log('\n📋 Latest Bali Coconut Living property:');
        console.log(`   Title: ${prop.title}`);
        console.log(`   Source URL: ${prop.source_url ? '✅ ' + prop.source_url : '❌ NULL'}`);
        console.log(`   Created: ${prop.created_at}`);
        
        if (prop.source_url) {
          console.log('\n🎉 SUCCESS: Source URL is properly stored by production scraper!');
        } else {
          console.log('\n❌ ISSUE: Source URL is still NULL even with production scraper');
        }
      }
    } else {
      console.log('ℹ️  No URLs were processed (queue might be empty)');
    }

  } catch (error) {
    console.error('💥 Error testing queue manager:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run if called directly
if (require.main === module) {
  testQueueManager()
    .then(() => {
      console.log('\n🎯 Queue manager test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Queue manager test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testQueueManager };
